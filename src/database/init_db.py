"""
Script khởi tạo database cho RedAI MCP Server.

Script này tạo tất cả các bảng trong database và có thể xóa dữ liệu cũ nếu cần.
"""

import asyncio
import argparse
from sqlalchemy import text

from .connection import db_manager
from .models import Base


async def create_tables(drop_existing: bool = False):
    """Tạo tất cả các bảng trong database."""
    
    print("Đang kết nối đến database...")
    
    # Kiểm tra kết nối
    if not await db_manager.test_connection():
        print("❌ Không thể kết nối đến database!")
        return False
    
    print("✅ Kết nối database thành công!")
    
    try:
        # Lấy engine để tạo bảng
        engine = db_manager.get_engine()
        
        if drop_existing:
            print("🗑️  Đang xóa tất cả bảng hiện có...")
            Base.metadata.drop_all(engine)
            print("✅ Đã xóa tất cả bảng!")
        
        print("🔨 Đang tạo các bảng...")
        Base.metadata.create_all(engine)
        print("✅ Đã tạo tất cả bảng thành công!")
        
        # Kiểm tra các bảng đã được tạo
        async with db_manager.create_async_session() as session:
            result = await session.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
            """))
            tables = result.fetchall()
            
            print("\n📋 Danh sách bảng đã tạo:")
            for table in tables:
                print(f"  - {table[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi tạo bảng: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await db_manager.close()


async def check_database_info():
    """Kiểm tra thông tin database."""
    
    print("📊 Thông tin database:")
    
    try:
        async with db_manager.create_async_session() as session:
            # Kiểm tra phiên bản PostgreSQL
            result = await session.execute(text("SELECT version()"))
            version = result.scalar()
            print(f"  - PostgreSQL: {version.split(',')[0]}")
            
            # Kiểm tra database hiện tại
            result = await session.execute(text("SELECT current_database()"))
            db_name = result.scalar()
            print(f"  - Database: {db_name}")
            
            # Kiểm tra user hiện tại
            result = await session.execute(text("SELECT current_user"))
            user = result.scalar()
            print(f"  - User: {user}")
            
            # Kiểm tra số lượng bảng
            result = await session.execute(text("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
            """))
            table_count = result.scalar()
            print(f"  - Số bảng: {table_count}")
            
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra thông tin database: {e}")


async def main():
    """Hàm main của script."""
    
    parser = argparse.ArgumentParser(description="Khởi tạo database cho RedAI MCP Server")
    parser.add_argument(
        "--drop", 
        action="store_true", 
        help="Xóa tất cả bảng hiện có trước khi tạo mới"
    )
    parser.add_argument(
        "--info", 
        action="store_true", 
        help="Chỉ hiển thị thông tin database"
    )
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🚀 KHỞI TẠO DATABASE CHO REDAI MCP SERVER")
    print("=" * 60)
    
    if args.info:
        await check_database_info()
        return
    
    if args.drop:
        confirm = input("⚠️  Bạn có chắc chắn muốn xóa tất cả dữ liệu? (y/N): ")
        if confirm.lower() != 'y':
            print("❌ Hủy bỏ thao tác!")
            return
    
    success = await create_tables(drop_existing=args.drop)
    
    if success:
        print("\n🎉 Khởi tạo database hoàn tất!")
        await check_database_info()
    else:
        print("\n❌ Khởi tạo database thất bại!")
        exit(1)


if __name__ == "__main__":
    asyncio.run(main())
