"""
HTTP Client cho Ahamove API

Module này cung cấp các hàm để thực hiện HTTP requests tới Ahamove API
với xử lý authentication Bearer Token và các tính năng đặc biệt của Ahamove.
"""

import asyncio
import json
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin, urlencode

# Import với fallback
try:
    from .config import AHAMOVE_BASE_URL, AHAMOVE_DEFAULT_HEADERS, AHAMOVE_TOKEN, AHAMOVE_API_KEY
except ImportError:
    try:
        from config import AHAMOVE_BASE_URL, AHAMOVE_DEFAULT_HEADERS, AHAMOVE_TOKEN, AHAMOVE_API_KEY
    except ImportError:
        # Fallback cuối cùng
        import os
        import sys
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        from config import AHAMOVE_BASE_URL, AHAMOVE_DEFAULT_HEADERS, AHAMOVE_TOKEN, AHAMOVE_API_KEY

# Sử dụng MCP's HTTP utilities
try:
    from mcp.shared._httpx_utils import httpx_client
except ImportError:
    # Fallback nếu không có MCP utilities
    import httpx
    httpx_client = httpx.AsyncClient()

async def make_ahamove_request(
    endpoint: str,
    method: str = "GET",
    data: Optional[Dict[str, Any]] = None,
    params: Optional[Dict[str, Any]] = None,
    headers: Optional[Dict[str, str]] = None,
    use_auth: bool = True,
    timeout: int = 30
) -> Dict[str, Any]:
    """
    Thực hiện HTTP request tới Ahamove API
    
    Args:
        endpoint: API endpoint (ví dụ: "orders" hoặc "cities")
        method: HTTP method (GET, POST, PUT, DELETE)
        data: Dữ liệu gửi trong body (cho POST/PUT)
        params: Query parameters
        headers: Headers bổ sung
        use_auth: Sử dụng Bearer Token authentication
        timeout: Timeout trong giây
        
    Returns:
        Dict chứa response data
        
    Raises:
        Exception: Khi có lỗi API hoặc network
    """
    
    # Chuẩn bị headers
    request_headers = AHAMOVE_DEFAULT_HEADERS.copy()
    if headers:
        request_headers.update(headers)
    
    # Thêm Bearer Token nếu cần
    if use_auth and AHAMOVE_TOKEN:
        request_headers["Authorization"] = f"Bearer {AHAMOVE_TOKEN}"
    
    # Tạo URL đầy đủ
    url = urljoin(AHAMOVE_BASE_URL, endpoint)
    
    # Thêm query parameters nếu có
    if params:
        url += "?" + urlencode(params)
    
    try:
        # Thực hiện request
        if method.upper() == "GET":
            response = await httpx_client.get(
                url,
                headers=request_headers,
                timeout=timeout
            )
        elif method.upper() == "POST":
            response = await httpx_client.post(
                url,
                headers=request_headers,
                json=data if data else {},
                timeout=timeout
            )
        elif method.upper() == "PUT":
            response = await httpx_client.put(
                url,
                headers=request_headers,
                json=data if data else {},
                timeout=timeout
            )
        elif method.upper() == "DELETE":
            response = await httpx_client.delete(
                url,
                headers=request_headers,
                timeout=timeout
            )
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
        
        # Kiểm tra status code
        response.raise_for_status()
        
        # Xử lý response
        content_type = response.headers.get("content-type", "")
        
        if "application/json" in content_type:
            # Response JSON
            result = response.json()
            return result
        else:
            # Response text hoặc khác
            return {
                "success": True,
                "content_type": content_type,
                "content": response.text
            }
            
    except Exception as e:
        # Log lỗi và re-raise
        error_msg = f"Ahamove API request failed: {str(e)}"
        print(f"❌ {error_msg}")
        print(f"   URL: {url}")
        print(f"   Method: {method}")
        print(f"   Headers: {request_headers}")
        if data:
            print(f"   Data: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        raise Exception(error_msg)

# Các hàm helper cho Account APIs

async def ahamove_register_account(mobile: str, name: str, address: str) -> Dict[str, Any]:
    """Đăng ký tài khoản Ahamove mới"""
    account_data = {
        "api_key": AHAMOVE_API_KEY,
        "mobile": mobile,
        "name": name,
        "address": address
    }
    return await make_ahamove_request("accounts", method="POST", data=account_data, use_auth=False)

async def ahamove_authenticate_token(mobile: str) -> Dict[str, Any]:
    """Xác thực và lấy token mới"""
    auth_data = {
        "api_key": AHAMOVE_API_KEY,
        "mobile": mobile
    }
    return await make_ahamove_request("accounts/token", method="POST", data=auth_data, use_auth=False)

async def ahamove_add_child_account(child_id: str) -> Dict[str, Any]:
    """Thêm tài khoản con"""
    params = {"child_id": child_id}
    return await make_ahamove_request("accounts/childs", method="POST", params=params)

async def ahamove_activate_child_account(child_id: str, activation_code: str) -> Dict[str, Any]:
    """Kích hoạt tài khoản con"""
    activate_data = {
        "child_id": child_id,
        "activation_code": activation_code
    }
    return await make_ahamove_request(f"accounts/childs/{child_id}/activate", method="POST", data=activate_data)

async def ahamove_remove_child_account(child_id: str) -> Dict[str, Any]:
    """Xóa tài khoản con"""
    return await make_ahamove_request(f"accounts/childs/{child_id}", method="DELETE")

async def ahamove_get_child_accounts() -> Dict[str, Any]:
    """Lấy danh sách tài khoản con"""
    return await make_ahamove_request("accounts/childs")

async def ahamove_update_profile(name: str = None, email: str = None) -> Dict[str, Any]:
    """Cập nhật thông tin tài khoản"""
    profile_data = {}
    if name:
        profile_data["name"] = name
    if email:
        profile_data["email"] = email
    
    return await make_ahamove_request("accounts", method="PUT", data=profile_data)

# Các hàm helper cho Master Data APIs

async def ahamove_get_cities(country_id: str = "VN") -> Dict[str, Any]:
    """Lấy danh sách thành phố"""
    params = {"country_id": country_id}
    return await make_ahamove_request("cities", params=params)

async def ahamove_get_services(city_id: str = None, lat: str = None, lng: str = None, delivery_type: str = None) -> Dict[str, Any]:
    """Lấy danh sách dịch vụ"""
    params = {}
    if city_id:
        params["city_id"] = city_id
    if lat:
        params["lat"] = lat
    if lng:
        params["lng"] = lng
    if delivery_type:
        params["delivery_type"] = delivery_type
    
    return await make_ahamove_request("services", params=params)

async def ahamove_get_service_details(service_id: str) -> Dict[str, Any]:
    """Lấy chi tiết dịch vụ"""
    return await make_ahamove_request(f"services/{service_id}")

# Các hàm helper cho Order APIs

async def ahamove_estimate_order_fee(order_data: Dict[str, Any]) -> Dict[str, Any]:
    """Ước tính phí đơn hàng"""
    return await make_ahamove_request("orders/estimates", method="POST", data=order_data)

async def ahamove_create_order(order_data: Dict[str, Any]) -> Dict[str, Any]:
    """Tạo đơn hàng mới"""
    return await make_ahamove_request("orders", method="POST", data=order_data)

async def ahamove_get_order_detail(order_id: str) -> Dict[str, Any]:
    """Lấy chi tiết đơn hàng"""
    return await make_ahamove_request(f"orders/{order_id}")

async def ahamove_cancel_order(order_id: str, comment: str = "") -> Dict[str, Any]:
    """Hủy đơn hàng"""
    cancel_data = {}
    if comment:
        cancel_data["comment"] = comment
    
    return await make_ahamove_request(f"orders/{order_id}/cancel", method="PUT", data=cancel_data)

async def ahamove_get_order_list(
    offset: int = 0,
    limit: int = 50,
    statuses: List[str] = None,
    from_date: str = None,
    to_date: str = None
) -> Dict[str, Any]:
    """Lấy danh sách đơn hàng"""
    params = {
        "offset": offset,
        "limit": limit
    }
    
    if statuses:
        params["statuses"] = ",".join(statuses)
    if from_date:
        params["from_date"] = from_date
    if to_date:
        params["to_date"] = to_date
    
    return await make_ahamove_request("orders", params=params)

async def ahamove_get_order_tracking(order_id: str) -> Dict[str, Any]:
    """Lấy thông tin tracking đơn hàng"""
    return await make_ahamove_request(f"orders/{order_id}/tracking")
