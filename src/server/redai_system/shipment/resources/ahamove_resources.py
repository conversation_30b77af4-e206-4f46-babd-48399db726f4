"""
Ahamove Resources cho Shipment MCP Server

Module này chứa các resources để lấy dữ liệu từ Ahamove API
"""

import json
from mcp.server.fastmcp import FastMCP

# Import với fallback
try:
    from src.server.redai_system.shipment.utils.ahamove_client import (
        ahamove_get_cities,
        ahamove_get_services,
        ahamove_get_service_details,
        ahamove_get_order_detail,
        ahamove_get_child_accounts,
        ahamove_get_order_tracking
    )
except ImportError:
    from utils.ahamove_client import (
        ahamove_get_cities,
        ahamove_get_services,
        ahamove_get_service_details,
        ahamove_get_order_detail,
        ahamove_get_child_accounts,
        ahamove_get_order_tracking
    )

def register_ahamove_resources(mcp: FastMCP):
    """Đăng ký tất cả Ahamove resources với MCP server"""

    @mcp.resource("ahamove://cities")
    async def get_ahamove_cities() -> str:
        """
        Lấy danh sách thành phố Ahamove hỗ trợ tại Việt Nam
        
        Resource này trả về danh sách các thành phố mà Ahamove đang
        cung cấp dịch vụ, bao gồm mã thành phố và tên tiếng Việt.
        
        Returns:
            JSON string chứa danh sách thành phố
        """
        try:
            result = await ahamove_get_cities("VN")
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách thành phố Ahamove: {str(e)}"

    @mcp.resource("ahamove://cities/{country_id}")
    async def get_ahamove_cities_by_country(country_id: str) -> str:
        """
        Lấy danh sách thành phố Ahamove theo quốc gia
        
        Args:
            country_id: Mã quốc gia (ví dụ: VN, TH, SG)
        
        Returns:
            JSON string chứa danh sách thành phố
        """
        try:
            result = await ahamove_get_cities(country_id)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách thành phố Ahamove cho {country_id}: {str(e)}"

    @mcp.resource("ahamove://services/{city_id}")
    async def get_ahamove_services_by_city(city_id: str) -> str:
        """
        Lấy danh sách dịch vụ Ahamove theo thành phố
        
        Args:
            city_id: Mã thành phố (ví dụ: SGN, HAN)
        
        Returns:
            JSON string chứa danh sách dịch vụ và yêu cầu đặc biệt
        """
        try:
            result = await ahamove_get_services(city_id=city_id)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy dịch vụ Ahamove cho thành phố {city_id}: {str(e)}"

    @mcp.resource("ahamove://service/{service_id}")
    async def get_ahamove_service_detail(service_id: str) -> str:
        """
        Lấy chi tiết dịch vụ Ahamove
        
        Args:
            service_id: Mã dịch vụ (ví dụ: SGN-BIKE, HAN-TRICYCLE)
        
        Returns:
            JSON string chứa chi tiết dịch vụ, phí và yêu cầu đặc biệt
        """
        try:
            result = await ahamove_get_service_details(service_id)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy chi tiết dịch vụ Ahamove {service_id}: {str(e)}"

    @mcp.resource("ahamove://order/{order_id}")
    async def get_ahamove_order_detail(order_id: str) -> str:
        """
        Lấy chi tiết đơn hàng Ahamove
        
        Args:
            order_id: Mã đơn hàng Ahamove
        
        Returns:
            JSON string chứa thông tin chi tiết đơn hàng
        """
        try:
            result = await ahamove_get_order_detail(order_id)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy chi tiết đơn hàng Ahamove {order_id}: {str(e)}"

    @mcp.resource("ahamove://order/{order_id}/tracking")
    async def get_ahamove_order_tracking(order_id: str) -> str:
        """
        Lấy thông tin tracking đơn hàng Ahamove
        
        Args:
            order_id: Mã đơn hàng Ahamove
        
        Returns:
            JSON string chứa thông tin tracking chi tiết
        """
        try:
            result = await ahamove_get_order_tracking(order_id)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy tracking đơn hàng Ahamove {order_id}: {str(e)}"

    @mcp.resource("ahamove://child-accounts")
    async def get_ahamove_child_accounts() -> str:
        """
        Lấy danh sách tài khoản con Ahamove
        
        Resource này trả về danh sách tất cả tài khoản con đã được
        liên kết với tài khoản cha hiện tại.
        
        Returns:
            JSON string chứa danh sách tài khoản con
        """
        try:
            result = await ahamove_get_child_accounts()
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách tài khoản con Ahamove: {str(e)}"

    @mcp.resource("ahamove://service-types")
    async def get_ahamove_service_types() -> str:
        """
        Thông tin về các loại dịch vụ Ahamove
        
        Returns:
            JSON string chứa thông tin các loại dịch vụ
        """
        service_types = {
            "delivery_types": {
                "INSTANT": {
                    "name": "Giao hàng tức thì",
                    "description": "Giao hàng trong vòng 1-2 giờ",
                    "suitable_for": "Hàng hóa nhỏ, gấp"
                },
                "TRUCK": {
                    "name": "Xe tải",
                    "description": "Vận chuyển hàng cồng kềnh",
                    "suitable_for": "Nội thất, hàng lớn"
                },
                "FOOD": {
                    "name": "Giao đồ ăn",
                    "description": "Chuyên giao thức ăn",
                    "suitable_for": "Nhà hàng, quán ăn"
                }
            },
            "common_services": {
                "SGN-BIKE": "Xe máy TP.HCM",
                "SGN-TRICYCLE": "Xe ba gác TP.HCM",
                "SGN-TRUCK": "Xe tải TP.HCM",
                "HAN-BIKE": "Xe máy Hà Nội",
                "HAN-TRICYCLE": "Xe ba gác Hà Nội",
                "HAN-TRUCK": "Xe tải Hà Nội"
            },
            "special_requests": {
                "TIP": "Tiền boa tài xế",
                "BULKY": "Hàng cồng kềnh (có phân tầng)",
                "D2D": "Giao tận tay",
                "FRAGILE": "Hàng dễ vỡ",
                "SMS": "Gửi SMS thông báo",
                "INSURANCE": "Bảo hiểm COD",
                "COLD": "Giữ lạnh",
                "RETURN": "Cho phép hoàn trả"
            }
        }
        
        return json.dumps(service_types, ensure_ascii=False, indent=2)

    @mcp.resource("ahamove://payment-methods")
    async def get_ahamove_payment_methods() -> str:
        """
        Thông tin về các phương thức thanh toán Ahamove
        
        Returns:
            JSON string chứa thông tin phương thức thanh toán
        """
        payment_methods = {
            "methods": {
                "CASH": {
                    "name": "Tiền mặt",
                    "description": "Người gửi trả tiền mặt cho tài xế",
                    "when_to_use": "Khi người gửi có mặt tại điểm lấy hàng"
                },
                "BALANCE": {
                    "name": "Tài khoản Ahamove",
                    "description": "Trừ tiền từ tài khoản Ahamove",
                    "when_to_use": "Khi đã nạp tiền vào tài khoản"
                },
                "CASH_BY_RECIPIENT": {
                    "name": "Người nhận trả",
                    "description": "Người nhận trả phí ship cho tài xế",
                    "when_to_use": "Khi người nhận đồng ý trả phí"
                }
            },
            "cod_support": {
                "description": "Thu hộ tiền hàng (COD)",
                "max_amount": "Tùy theo dịch vụ và thành phố",
                "note": "Tài xế thu tiền hàng và chuyển về cho người gửi"
            }
        }
        
        return json.dumps(payment_methods, ensure_ascii=False, indent=2)

    @mcp.resource("ahamove://order-statuses")
    async def get_ahamove_order_statuses() -> str:
        """
        Danh sách trạng thái đơn hàng Ahamove
        
        Returns:
            JSON string chứa thông tin các trạng thái đơn hàng
        """
        order_statuses = {
            "statuses": {
                "PENDING": "Chờ xử lý - Đơn vừa được tạo",
                "ASSIGNING": "Đang tìm tài xế - Hệ thống đang giao đơn cho tài xế",
                "ACCEPTED": "Đã nhận đơn - Tài xế đã nhận và đang đến lấy hàng",
                "PICKING": "Đang lấy hàng - Tài xế đã đến điểm lấy hàng",
                "PICKED_UP": "Đã lấy hàng - Tài xế đã lấy hàng và đang giao",
                "IN_DELIVERY": "Đang giao hàng - Tài xế đang trên đường giao",
                "DELIVERED": "Đã giao - Đã giao hàng thành công tại điểm cuối",
                "COMPLETED": "Hoàn thành - Đơn hàng đã hoàn tất",
                "CANCELLED": "Đã hủy - Đơn hàng bị hủy bởi người dùng hoặc tài xế",
                "FAILED": "Thất bại - Giao hàng thất bại"
            },
            "flow": [
                "PENDING → ASSIGNING → ACCEPTED → PICKING → PICKED_UP → IN_DELIVERY → DELIVERED → COMPLETED",
                "Có thể chuyển sang CANCELLED hoặc FAILED ở bất kỳ bước nào"
            ],
            "cancellation": {
                "by_user": "Người dùng có thể hủy trước khi tài xế lấy hàng",
                "by_driver": "Tài xế có thể hủy với lý do hợp lệ",
                "auto_cancel": "Hệ thống tự hủy nếu không tìm được tài xế"
            }
        }
        
        return json.dumps(order_statuses, ensure_ascii=False, indent=2)

    @mcp.resource("ahamove://api-guide")
    async def get_ahamove_api_guide() -> str:
        """
        Hướng dẫn sử dụng Ahamove API
        
        Returns:
            JSON string chứa hướng dẫn chi tiết
        """
        api_guide = {
            "authentication": {
                "method": "Bearer Token",
                "steps": [
                    "1. Đăng ký tài khoản với API key",
                    "2. Lấy token qua API authenticate",
                    "3. Sử dụng token trong header Authorization: Bearer <token>"
                ],
                "token_expiry": "Token có thời hạn, cần refresh khi hết hạn"
            },
            "account_management": {
                "parent_child": "Hỗ trợ tài khoản cha-con",
                "child_activation": "Tài khoản con cần kích hoạt qua SMS",
                "benefits": "Quản lý tập trung, phân quyền linh hoạt"
            },
            "order_creation": {
                "required_fields": ["order_time", "path", "service_id", "payment_method"],
                "path_minimum": "Ít nhất 2 điểm (pickup + delivery)",
                "address_format": "Địa chỉ đầy đủ: Số nhà, Đường, Phường, Quận, Thành phố",
                "coordinates": "Lat/lng bắt buộc và chính xác"
            },
            "special_features": {
                "route_optimization": "Tối ưu lộ trình cho đơn nhiều điểm",
                "group_services": "Sử dụng group_service_id thay vì service_id cụ thể",
                "promo_codes": "Hỗ trợ mã khuyến mãi",
                "cod_support": "Thu hộ tiền hàng"
            },
            "best_practices": [
                "Luôn estimate trước khi tạo đơn",
                "Kiểm tra service availability theo thành phố",
                "Sử dụng địa chỉ chính xác và đầy đủ",
                "Cung cấp số điện thoại người nhận",
                "Test với staging environment trước",
                "Handle webhook để cập nhật trạng thái real-time"
            ],
            "common_errors": [
                "INVALID_MAX_DISTANCE: Quãng đường vượt quá giới hạn",
                "SERVICE_NOT_VALID_AT_PICKUP: Dịch vụ không khả dụng tại điểm lấy",
                "NOT_ENOUGH_CREDIT: Tài khoản không đủ tiền",
                "INVALID_PICKUP_AREA: Khu vực lấy hàng không hợp lệ",
                "DUPLICATE_TRACKING_NUMBER: Mã tracking bị trùng"
            ]
        }
        
        return json.dumps(api_guide, ensure_ascii=False, indent=2)
