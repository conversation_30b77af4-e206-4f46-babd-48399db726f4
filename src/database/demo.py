"""
Demo script để test kết nối database và các truy vấn cơ bản.

Sc<PERSON>t này thực hiện các thao tác CRUD cơ bản để kiểm tra database hoạt động.
"""

import asyncio
from datetime import datetime

from .connection import db_manager, get_db_session
from .repository import UserRepository, AgentRepository, ToolRepository, ResourceRepository


async def test_connection():
    """Test kết nối database."""
    print("🔌 Đang test kết nối database...")

    success = await db_manager.test_connection()
    if success:
        print("✅ Kết nối database thành công!")
        return True
    else:
        print("❌ Kết nối database thất bại!")
        return False


async def demo_user_operations():
    """Demo các thao tác với User."""
    print("\n👥 DEMO THAO TÁC VỚI USER")
    print("-" * 40)

    async with get_db_session() as session:
        user_repo = UserRepository(session)

        # Tạo user mới
        print("📝 Tạo user mới...")
        user_data = {
            "username": "demo_user",
            "email": "<EMAIL>",
            "full_name": "Demo User",
            "is_active": True,
            "api_key": "demo_api_key_123"
        }

        # Kiểm tra user đã tồn tại chưa
        existing_user = await user_repo.get_by_username(user_data["username"])
        if existing_user:
            print(f"  - User '{user_data['username']}' đã tồn tại, sử dụng user hiện có")
            user = existing_user
        else:
            user = await user_repo.create(**user_data)
            print(f"  - Đã tạo user: {user.username} (ID: {user.id})")

        # Lấy user theo ID
        print(f"\n🔍 Lấy user theo ID {user.id}...")
        found_user = await user_repo.get_by_id(user.id)
        if found_user:
            print(f"  - Tìm thấy: {found_user.username} - {found_user.email}")

        # Lấy user theo username
        print(f"\n🔍 Lấy user theo username '{user.username}'...")
        found_user = await user_repo.get_by_username(user.username)
        if found_user:
            print(f"  - Tìm thấy: {found_user.username} - {found_user.email}")

        # Lấy user theo API key
        print(f"\n🔍 Lấy user theo API key...")
        found_user = await user_repo.get_by_api_key(user.api_key)
        if found_user:
            print(f"  - Tìm thấy: {found_user.username} - {found_user.api_key}")

        # Cập nhật user
        print(f"\n✏️ Cập nhật user...")
        updated_user = await user_repo.update(user.id, full_name="Updated Demo User")
        if updated_user:
            print(f"  - Đã cập nhật: {updated_user.full_name}")

        # Lấy danh sách users
        print(f"\n📋 Lấy danh sách users...")
        users = await user_repo.get_all(limit=5)
        print(f"  - Tìm thấy {len(users)} users:")
        for u in users:
            print(f"    + {u.username} ({u.email})")

        return user


async def demo_agent_operations(user):
    """Demo các thao tác với Agent."""
    print("\n🤖 DEMO THAO TÁC VỚI AGENT")
    print("-" * 40)

    async with get_db_session() as session:
        agent_repo = AgentRepository(session)

        # Tạo agent mới
        print("📝 Tạo agent mới...")
        agent_data = {
            "name": "Demo Agent",
            "description": "Agent demo để test",
            "config": {
                "model": "gpt-4",
                "temperature": 0.7,
                "max_tokens": 2000
            },
            "user_id": user.id,
            "is_active": True
        }

        # Kiểm tra agent đã tồn tại chưa
        existing_agent = await agent_repo.get_by_name(user.id, agent_data["name"])
        if existing_agent:
            print(f"  - Agent '{agent_data['name']}' đã tồn tại, sử dụng agent hiện có")
            agent = existing_agent
        else:
            agent = await agent_repo.create(**agent_data)
            print(f"  - Đã tạo agent: {agent.name} (ID: {agent.id})")

        # Lấy agent theo ID
        print(f"\n🔍 Lấy agent theo ID {agent.id}...")
        found_agent = await agent_repo.get_by_id(agent.id)
        if found_agent:
            print(f"  - Tìm thấy: {found_agent.name} - {found_agent.description}")
            print(f"  - User: {found_agent.user.username}")

        # Lấy agents của user
        print(f"\n📋 Lấy agents của user {user.username}...")
        agents = await agent_repo.get_by_user_id(user.id)
        print(f"  - Tìm thấy {len(agents)} agents:")
        for a in agents:
            print(f"    + {a.name}")

        # Cập nhật agent
        print(f"\n✏️ Cập nhật agent...")
        updated_agent = await agent_repo.update(agent.id, description="Updated demo agent")
        if updated_agent:
            print(f"  - Đã cập nhật: {updated_agent.description}")

        return agent


async def demo_tool_operations(agent):
    """Demo các thao tác với Tool."""
    print("\n🔧 DEMO THAO TÁC VỚI TOOL")
    print("-" * 40)

    async with get_db_session() as session:
        tool_repo = ToolRepository(session)

        # Tạo tool mới
        print("📝 Tạo tool mới...")
        tool_data = {
            "name": "demo_calculator",
            "description": "Tool tính toán demo",
            "tool_type": "builtin",
            "config": {"precision": 2},
            "input_schema": {
                "type": "object",
                "properties": {
                    "expression": {"type": "string", "description": "Biểu thức toán học"}
                },
                "required": ["expression"]
            },
            "agent_id": agent.id,
            "is_active": True
        }

        # Kiểm tra tool đã tồn tại chưa
        existing_tool = await tool_repo.get_by_name(agent.id, tool_data["name"])
        if existing_tool:
            print(f"  - Tool '{tool_data['name']}' đã tồn tại, sử dụng tool hiện có")
            tool = existing_tool
        else:
            tool = await tool_repo.create(**tool_data)
            print(f"  - Đã tạo tool: {tool.name} (ID: {tool.id})")

        # Lấy tool theo ID
        print(f"\n🔍 Lấy tool theo ID {tool.id}...")
        found_tool = await tool_repo.get_by_id(tool.id)
        if found_tool:
            print(f"  - Tìm thấy: {found_tool.name} - {found_tool.description}")
            print(f"  - Type: {found_tool.tool_type}")

        # Lấy tools của agent
        print(f"\n📋 Lấy tools của agent {agent.name}...")
        tools = await tool_repo.get_by_agent_id(agent.id)
        print(f"  - Tìm thấy {len(tools)} tools:")
        for t in tools:
            print(f"    + {t.name} ({t.tool_type})")

        # Lấy tools theo type
        print(f"\n🔍 Lấy tools theo type 'builtin'...")
        builtin_tools = await tool_repo.get_by_type("builtin")
        print(f"  - Tìm thấy {len(builtin_tools)} builtin tools")

        return tool


async def demo_resource_operations(agent):
    """Demo các thao tác với Resource."""
    print("\n📄 DEMO THAO TÁC VỚI RESOURCE")
    print("-" * 40)

    async with get_db_session() as session:
        resource_repo = ResourceRepository(session)

        # Tạo resource mới
        print("📝 Tạo resource mới...")
        resource_data = {
            "uri": "file://demo/test.txt",
            "name": "Demo Text File",
            "description": "File text demo",
            "mime_type": "text/plain",
            "content": "Đây là nội dung demo của file text",
            "meta_data": {"encoding": "utf-8", "size": 100},
            "agent_id": agent.id,
            "is_active": True
        }

        # Kiểm tra resource đã tồn tại chưa
        existing_resource = await resource_repo.get_by_uri(agent.id, resource_data["uri"])
        if existing_resource:
            print(f"  - Resource '{resource_data['name']}' đã tồn tại, sử dụng resource hiện có")
            resource = existing_resource
        else:
            resource = await resource_repo.create(**resource_data)
            print(f"  - Đã tạo resource: {resource.name} (ID: {resource.id})")

        # Lấy resource theo ID
        print(f"\n🔍 Lấy resource theo ID {resource.id}...")
        found_resource = await resource_repo.get_by_id(resource.id)
        if found_resource:
            print(f"  - Tìm thấy: {found_resource.name} - {found_resource.uri}")
            print(f"  - MIME type: {found_resource.mime_type}")

        # Lấy resources của agent
        print(f"\n📋 Lấy resources của agent {agent.name}...")
        resources = await resource_repo.get_by_agent_id(agent.id)
        print(f"  - Tìm thấy {len(resources)} resources:")
        for r in resources:
            print(f"    + {r.name} ({r.mime_type})")

        return resource


async def main():
    """Hàm main của demo."""
    print("=" * 60)
    print("🚀 DEMO DATABASE OPERATIONS - REDAI MCP SERVER")
    print("=" * 60)

    try:
        # Test kết nối
        if not await test_connection():
            return

        # Demo các thao tác
        user = await demo_user_operations()
        agent = await demo_agent_operations(user)
        tool = await demo_tool_operations(agent)
        resource = await demo_resource_operations(agent)

        print("\n🎉 Demo hoàn tất thành công!")
        print("\n📊 Tóm tắt:")
        print(f"  - User: {user.username} (ID: {user.id})")
        print(f"  - Agent: {agent.name} (ID: {agent.id})")
        print(f"  - Tool: {tool.name} (ID: {tool.id})")
        print(f"  - Resource: {resource.name} (ID: {resource.id})")

    except Exception as e:
        print(f"\n❌ Lỗi trong quá trình demo: {e}")
        import traceback
        traceback.print_exc()

    finally:
        await db_manager.close()


if __name__ == "__main__":
    asyncio.run(main())
