"""
Quản lý kết nối database cho RedAI MCP Server.

Mo<PERSON>le này cung cấp các chức năng để tạo và quản lý kết nối database với SQLAlchemy.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool, NullPool

from .config import db_config, DATABASE_URL, ASYNC_DATABASE_URL


class DatabaseManager:
    """Quản lý kết nối database với SQLAlchemy."""

    def __init__(self):
        self._engine = None
        self._async_engine = None
        self._session_factory = None
        self._async_session_factory = None

    def get_engine(self):
        """Lấy SQLAlchemy engine (đồng bộ)."""
        if self._engine is None:
            self._engine = create_engine(
                DATABASE_URL,
                poolclass=QueuePool,
                pool_size=db_config.pool_size,
                max_overflow=db_config.max_overflow,
                pool_timeout=db_config.pool_timeout,
                pool_recycle=db_config.pool_recycle,
                echo=db_config.echo,
                # Cấu hình cho PostgreSQL
                connect_args={
                    "connect_timeout": 10,
                    "application_name": "redai_mcp_server"
                }
            )
        return self._engine

    def get_async_engine(self):
        """Lấy SQLAlchemy async engine."""
        if self._async_engine is None:
            self._async_engine = create_async_engine(
                ASYNC_DATABASE_URL,
                poolclass=NullPool,  # Sử dụng NullPool cho async engine
                echo=db_config.echo,
                # Cấu hình cho PostgreSQL với asyncpg
                connect_args={
                    "command_timeout": 10,
                    "server_settings": {
                        "application_name": "redai_mcp_server_async"
                    }
                }
            )
        return self._async_engine

    def get_session_factory(self):
        """Lấy session factory (đồng bộ)."""
        if self._session_factory is None:
            self._session_factory = sessionmaker(
                bind=self.get_engine(),
                autocommit=False,
                autoflush=False
            )
        return self._session_factory

    def get_async_session_factory(self):
        """Lấy async session factory."""
        if self._async_session_factory is None:
            self._async_session_factory = async_sessionmaker(
                bind=self.get_async_engine(),
                class_=AsyncSession,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False
            )
        return self._async_session_factory

    def create_session(self) -> Session:
        """Tạo session mới (đồng bộ)."""
        return self.get_session_factory()()

    def create_async_session(self) -> AsyncSession:
        """Tạo async session mới."""
        return self.get_async_session_factory()()

    async def test_connection(self) -> bool:
        """Kiểm tra kết nối database."""
        try:
            async with self.create_async_session() as session:
                result = await session.execute(text("SELECT 1"))
                return result.scalar() == 1
        except Exception as e:
            print(f"Lỗi kết nối database: {e}")
            return False

    async def close(self):
        """Đóng tất cả kết nối database."""
        if self._async_engine:
            await self._async_engine.dispose()
        if self._engine:
            self._engine.dispose()


# Tạo instance global
db_manager = DatabaseManager()


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Context manager để lấy database session."""
    session = db_manager.create_async_session()
    try:
        yield session
        await session.commit()
    except Exception:
        await session.rollback()
        raise
    finally:
        await session.close()


async def get_db_session_dependency() -> AsyncGenerator[AsyncSession, None]:
    """Dependency function cho FastAPI hoặc các framework khác."""
    async with get_db_session() as session:
        yield session
