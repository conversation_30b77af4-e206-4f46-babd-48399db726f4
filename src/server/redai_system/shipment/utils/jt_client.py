"""
HTTP Client cho J&T Express API

Module này cung cấp các hàm để thực hiện HTTP requests tới J&T Express API
với xử lý authentication, data signing và form encoding đặc biệt của J&T.
"""

import asyncio
import json
import hashlib
import base64
from typing import Dict, Any, Optional, List
from urllib.parse import urlencode

# Import với fallback
try:
    from .config import JT_BASE_URL, JT_DEFAULT_HEADERS, JT_USERNAME, JT_API_KEY, JT_CUSTOMER_CODE
except ImportError:
    try:
        from config import JT_BASE_URL, JT_DEFAULT_HEADERS, JT_USERNAME, JT_API_KEY, JT_CUSTOMER_CODE
    except ImportError:
        # Fallback cuối cùng
        import os
        import sys
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        from config import JT_BASE_URL, JT_DEFAULT_HEADERS, JT_USERNAME, JT_API_KEY, JT_CUSTOMER_CODE

# Sử dụng MCP's HTTP utilities
try:
    from mcp.shared._httpx_utils import httpx_client
except ImportError:
    # Fallback nếu không có MCP utilities
    import httpx
    httpx_client = httpx.AsyncClient()

def create_data_signature(data_param: str, api_key: str) -> str:
    """
    Tạo chữ ký bảo mật cho J&T API
    
    Args:
        data_param: Dữ liệu JSON string
        api_key: API key
        
    Returns:
        Chữ ký MD5 + Base64
    """
    # Kết hợp data_param + api_key
    combined = data_param + api_key
    
    # Tạo MD5 hash
    md5_hash = hashlib.md5(combined.encode('utf-8')).hexdigest()
    
    # Encode Base64
    signature = base64.b64encode(md5_hash.encode('utf-8')).decode('utf-8')
    
    return signature

async def make_jt_request(
    endpoint: str,
    data: Dict[str, Any],
    method: str = "POST",
    use_basic_auth: bool = False,
    timeout: int = 30
) -> Dict[str, Any]:
    """
    Thực hiện HTTP request tới J&T Express API
    
    Args:
        endpoint: API endpoint (ví dụ: "order/add")
        data: Dữ liệu gửi
        method: HTTP method (chỉ hỗ trợ POST)
        use_basic_auth: Sử dụng Basic Auth (cho tracking API)
        timeout: Timeout trong giây
        
    Returns:
        Dict chứa response data
        
    Raises:
        Exception: Khi có lỗi API hoặc network
    """
    
    if method.upper() != "POST":
        raise ValueError("J&T API chỉ hỗ trợ POST method")
    
    # Tạo URL đầy đủ
    url = f"{JT_BASE_URL}/{endpoint}"
    
    # Chuẩn bị headers
    headers = JT_DEFAULT_HEADERS.copy()
    
    try:
        if use_basic_auth:
            # Sử dụng Basic Auth cho tracking API
            import base64
            credentials = base64.b64encode(f"{JT_USERNAME}:{JT_API_KEY}".encode()).decode()
            headers["Authorization"] = f"Basic {credentials}"
            
            # Form data cho tracking
            form_data = data
            
        else:
            # Sử dụng data signing cho các API khác
            data_param = json.dumps(data, separators=(',', ':'))
            data_sign = create_data_signature(data_param, JT_API_KEY)
            
            # Form data với signature
            form_data = {
                "data_param": data_param,
                "data_sign": data_sign
            }
        
        # Thực hiện request
        response = await httpx_client.post(
            url,
            headers=headers,
            data=form_data,
            timeout=timeout
        )
        
        # Kiểm tra status code
        response.raise_for_status()
        
        # Xử lý response
        content_type = response.headers.get("content-type", "")
        
        if "application/json" in content_type:
            result = response.json()
        else:
            # Thử parse JSON từ text response
            try:
                result = json.loads(response.text)
            except json.JSONDecodeError:
                result = {
                    "success": False,
                    "message": "Invalid JSON response",
                    "raw_response": response.text
                }
        
        return result
        
    except Exception as e:
        # Log lỗi và re-raise
        error_msg = f"J&T API request failed: {str(e)}"
        print(f"❌ {error_msg}")
        print(f"   URL: {url}")
        print(f"   Method: {method}")
        print(f"   Headers: {headers}")
        if 'form_data' in locals():
            # Ẩn sensitive data khi log
            safe_data = form_data.copy()
            if 'data_sign' in safe_data:
                safe_data['data_sign'] = '***HIDDEN***'
            print(f"   Data: {safe_data}")
        
        raise Exception(error_msg)

# Các hàm helper cho các API endpoint cụ thể

async def jt_create_order(order_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Tạo đơn hàng J&T Express
    
    Args:
        order_data: Dữ liệu đơn hàng (mảng các đơn)
        
    Returns:
        Response từ J&T API
    """
    return await make_jt_request("order/add", order_data)

async def jt_track_order(awb: str, eccompanyid: str = None) -> Dict[str, Any]:
    """
    Tra cứu trạng thái đơn hàng J&T
    
    Args:
        awb: Mã vận đơn (AWB)
        eccompanyid: Mã cửa hàng/đối tác
        
    Returns:
        Response từ J&T API
    """
    if not eccompanyid:
        eccompanyid = JT_CUSTOMER_CODE
    
    tracking_data = {
        "awb": awb,
        "eccompanyid": eccompanyid
    }
    
    return await make_jt_request("order/track", tracking_data, use_basic_auth=True)

async def jt_calculate_tariff(
    weight: float,
    send_site_code: str,
    dest_area_code: str,
    product_type: str = "EZ"
) -> Dict[str, Any]:
    """
    Tính cước vận chuyển J&T
    
    Args:
        weight: Trọng lượng (kg)
        send_site_code: Mã tỉnh/thành gốc
        dest_area_code: Mã quận/huyện đích
        product_type: Loại sản phẩm (mặc định "EZ")
        
    Returns:
        Response từ J&T API
    """
    tariff_data = {
        "weight": weight,
        "sendSiteCode": send_site_code,
        "destAreaCode": dest_area_code,
        "cusName": JT_CUSTOMER_CODE,
        "productType": product_type
    }
    
    # Sử dụng endpoint khác cho tariff checking
    url = f"{JT_BASE_URL}/tariff/check"
    
    # Tạo signature cho tariff data
    data_json = json.dumps(tariff_data, separators=(',', ':'))
    signature = create_data_signature(data_json, JT_API_KEY)
    
    form_data = {
        "data": data_json,
        "sign": signature
    }
    
    try:
        response = await httpx_client.post(
            url,
            headers=JT_DEFAULT_HEADERS,
            data=form_data,
            timeout=30
        )
        
        response.raise_for_status()
        result = response.json()
        
        return result
        
    except Exception as e:
        error_msg = f"J&T tariff calculation failed: {str(e)}"
        print(f"❌ {error_msg}")
        raise Exception(error_msg)

async def jt_cancel_order(order_id: str, remark: str = "Canceled by system") -> Dict[str, Any]:
    """
    Hủy đơn hàng J&T Express
    
    Args:
        order_id: Mã đơn hàng cần hủy
        remark: Lý do hủy đơn
        
    Returns:
        Response từ J&T API
    """
    cancel_data = [{
        "username": JT_USERNAME,
        "api_key": JT_API_KEY,
        "orderid": order_id,
        "remark": remark
    }]
    
    return await make_jt_request("order/cancel", cancel_data)

# Hàm helper để tạo đơn hàng với đầy đủ thông tin
def create_jt_order_data(
    order_id: str,
    shipper_info: Dict[str, str],
    receiver_info: Dict[str, str],
    package_info: Dict[str, Any],
    service_info: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    Tạo dữ liệu đơn hàng J&T với format đúng
    
    Args:
        order_id: Mã đơn hàng
        shipper_info: Thông tin người gửi
        receiver_info: Thông tin người nhận
        package_info: Thông tin gói hàng
        service_info: Thông tin dịch vụ
        
    Returns:
        Mảng dữ liệu đơn hàng J&T
    """
    
    order_data = [{
        "username": JT_USERNAME,
        "api_key": JT_API_KEY,
        "orderid": order_id,
        
        # Thông tin người gửi
        "shipper_name": shipper_info.get("name"),
        "shipper_contact": shipper_info.get("contact", shipper_info.get("name")),
        "shipper_phone": shipper_info.get("phone"),
        "shipper_addr": shipper_info.get("address"),
        "origin_code": shipper_info.get("city_code"),
        
        # Thông tin người nhận
        "receiver_name": receiver_info.get("name"),
        "receiver_phone": receiver_info.get("phone"),
        "receiver_addr": receiver_info.get("address"),
        "receiver_zip": receiver_info.get("zipcode", "00000"),
        "destination_code": receiver_info.get("city_code"),
        "receiver_area": receiver_info.get("area_code"),
        
        # Thông tin gói hàng
        "qty": package_info.get("quantity", 1),
        "weight": package_info.get("weight"),
        "goodsdesc": package_info.get("description"),
        "item_name": package_info.get("item_name"),
        "goodsvalue": package_info.get("value", 0),
        
        # Thông tin dịch vụ
        "servicetype": service_info.get("service_type", 1),  # 1=Pickup, 6=Drop-off
        "insurance": service_info.get("insurance", 0),
        "cod": service_info.get("cod", 0),
        "expresstype": "1",  # Luôn là "1" cho EZ service
        
        # Thông tin thời gian
        "orderdate": service_info.get("order_date"),
        "sendstarttime": service_info.get("pickup_start_time"),
        "sendendtime": service_info.get("pickup_end_time")
    }]
    
    return order_data
