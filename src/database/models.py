"""
Database models cho RedAI MCP Server.

Module n<PERSON><PERSON> định nghĩa các model SQLAlchemy cho cơ sở dữ liệu.
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean,
    ForeignKey, JSON, Index, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func

# Tạo base class cho tất cả models
Base = declarative_base()


class TimestampMixin:
    """Mixin để thêm timestamp cho các model."""

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False
    )


class User(Base, TimestampMixin):
    """Model cho bảng users."""

    __tablename__ = "users"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    email: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    full_name: Mapped[Optional[str]] = mapped_column(String(100))
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    api_key: Mapped[Optional[str]] = mapped_column(String(255), unique=True)

    # Relationships
    agents: Mapped[List["Agent"]] = relationship("Agent", back_populates="user", cascade="all, delete-orphan")

    # Indexes
    __table_args__ = (
        Index("idx_users_username", "username"),
        Index("idx_users_email", "email"),
        Index("idx_users_api_key", "api_key"),
    )

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"


class Agent(Base, TimestampMixin):
    """Model cho bảng agents."""

    __tablename__ = "agents"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    config: Mapped[Optional[dict]] = mapped_column(JSON)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Foreign keys
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"), nullable=False)

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="agents")
    tools: Mapped[List["Tool"]] = relationship("Tool", back_populates="agent", cascade="all, delete-orphan")
    resources: Mapped[List["Resource"]] = relationship("Resource", back_populates="agent", cascade="all, delete-orphan")

    # Indexes
    __table_args__ = (
        Index("idx_agents_user_id", "user_id"),
        Index("idx_agents_name", "name"),
        UniqueConstraint("user_id", "name", name="uq_agents_user_name"),
    )

    def __repr__(self):
        return f"<Agent(id={self.id}, name='{self.name}', user_id={self.user_id})>"


class Tool(Base, TimestampMixin):
    """Model cho bảng tools."""

    __tablename__ = "tools"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    tool_type: Mapped[str] = mapped_column(String(50), nullable=False)  # 'builtin', 'integration', 'custom'
    config: Mapped[Optional[dict]] = mapped_column(JSON)
    input_schema: Mapped[Optional[dict]] = mapped_column(JSON)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Foreign keys
    agent_id: Mapped[int] = mapped_column(Integer, ForeignKey("agents.id"), nullable=False)

    # Relationships
    agent: Mapped["Agent"] = relationship("Agent", back_populates="tools")

    # Indexes
    __table_args__ = (
        Index("idx_tools_agent_id", "agent_id"),
        Index("idx_tools_name", "name"),
        Index("idx_tools_type", "tool_type"),
        UniqueConstraint("agent_id", "name", name="uq_tools_agent_name"),
    )

    def __repr__(self):
        return f"<Tool(id={self.id}, name='{self.name}', type='{self.tool_type}', agent_id={self.agent_id})>"


class Resource(Base, TimestampMixin):
    """Model cho bảng resources."""

    __tablename__ = "resources"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    uri: Mapped[str] = mapped_column(String(500), nullable=False)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    mime_type: Mapped[str] = mapped_column(String(100), nullable=False)
    content: Mapped[Optional[str]] = mapped_column(Text)
    meta_data: Mapped[Optional[dict]] = mapped_column(JSON)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Foreign keys
    agent_id: Mapped[int] = mapped_column(Integer, ForeignKey("agents.id"), nullable=False)

    # Relationships
    agent: Mapped["Agent"] = relationship("Agent", back_populates="resources")

    # Indexes
    __table_args__ = (
        Index("idx_resources_agent_id", "agent_id"),
        Index("idx_resources_uri", "uri"),
        Index("idx_resources_name", "name"),
        Index("idx_resources_mime_type", "mime_type"),
        UniqueConstraint("agent_id", "uri", name="uq_resources_agent_uri"),
    )

    def __repr__(self):
        return f"<Resource(id={self.id}, name='{self.name}', uri='{self.uri}', agent_id={self.agent_id})>"
