# RedAI MCP Server

Dự án này triển khai một máy chủ MCP (Model Context Protocol) sử dụng thư viện FastMCP để tích hợp với API hiện có thông qua đặc tả OpenAPI. Dự án cũng bao gồm cấu hình kết nối cơ sở dữ liệu để lưu trữ và quản lý dữ liệu.

## Cấu trúc dự án

```
redai-v201-mcp-server/
├── README.md                  # Tài liệu dự án
├── pyproject.toml             # Cấu hình dự án
├── .python-version            # Phiên bản Python
├── src/                       # Thư mục mã nguồn chính
│   ├── main.py                # Entry point chính
│   ├── server/                # Mã nguồn máy chủ
│   │   ├── main.py            # Entry point cho server
│   │   ├── redai_global/      # Module global
│   │   └── redai_system/      # Module hệ thống
│   ├── client/                # Mã nguồn client
│   │   └── main.py            # Entry point cho client
│   └── database/              # Module cơ sở dữ liệu
│       ├── config.py           # Cấu hình cơ sở dữ liệu
│       ├── connection.py       # Kết nối cơ sở dữ liệu
│       ├── models.py           # Các model cơ sở dữ liệu
│       ├── repository.py       # Các repository
│       ├── init_db.py          # Script khởi tạo cơ sở dữ liệu
│       └── seed_db.py          # Script tạo dữ liệu mẫu
├── config/                    # Cấu hình
│   ├── default.json           # Cấu hình mặc định
│   └── openapi_spec.json      # Đặc tả OpenAPI
├── tests/                     # Kiểm thử
└── examples/                  # Ví dụ sử dụng
    ├── database_example.py     # Ví dụ sử dụng cơ sở dữ liệu
    └── mcp_database_server.py  # Ví dụ máy chủ MCP với cơ sở dữ liệu
```

## Yêu cầu

- Python 3.13+
- FastMCP 2.3.0+
- SQLAlchemy 2.0+
- PostgreSQL, MySQL, SQLite hoặc MSSQL (tùy chọn)

## Cài đặt

```bash
# Cài đặt các phụ thuộc
pip install -e .
```

## Sử dụng

### Chạy máy chủ MCP

```bash
python -m src.main server
```

### Chạy client MCP

```bash
python -m src.main client
```

## Cấu hình

Cấu hình được lưu trong thư mục `config/`:

- `default.json`: Cấu hình mặc định cho máy chủ, client và cơ sở dữ liệu
- `openapi_spec.json`: Đặc tả OpenAPI của API cần tích hợp

### Cấu hình cơ sở dữ liệu

Cấu hình cơ sở dữ liệu được định nghĩa trong phần `database` của file `config/default.json`:

```json
"database": {
    "driver": "postgresql",
    "host": "localhost",
    "port": 5432,
    "username": "postgres",
    "password": "postgres",
    "database": "redai_mcp",
    "pool_size": 5,
    "max_overflow": 10,
    "pool_timeout": 30,
    "pool_recycle": 1800,
    "echo": false
}
```

Các driver được hỗ trợ:
- `postgresql`: PostgreSQL
- `mysql`: MySQL
- `sqlite`: SQLite
- `mssql`: Microsoft SQL Server

## Phát triển

### Thêm công cụ mới

Để thêm công cụ mới vào máy chủ MCP, bạn có thể tạo một module mới trong thư mục `src/server/` và sử dụng decorator `@mcp.tool()`.

### Thêm tài nguyên mới

Để thêm tài nguyên mới, sử dụng decorator `@mcp.resource()` hoặc cập nhật đặc tả OpenAPI.

### Khởi tạo cơ sở dữ liệu

Để khởi tạo cơ sở dữ liệu, sử dụng script `src/database/init_db.py`:

```bash
# Khởi tạo cơ sở dữ liệu
python -m src.database.init_db

# Khởi tạo cơ sở dữ liệu và xóa các bảng hiện có
python -m src.database.init_db --drop
```

### Tạo dữ liệu mẫu

Để tạo dữ liệu mẫu cho cơ sở dữ liệu, sử dụng script `src/database/seed_db.py`:

```bash
python -m src.database.seed_db
```

### Sử dụng cơ sở dữ liệu trong máy chủ MCP

Bạn có thể tham khảo ví dụ `examples/mcp_database_server.py` để biết cách sử dụng cơ sở dữ liệu trong máy chủ MCP.

## Giấy phép

[Thêm thông tin giấy phép của bạn ở đây]