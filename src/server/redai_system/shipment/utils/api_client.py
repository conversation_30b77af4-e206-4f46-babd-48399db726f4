"""
HTTP Client cho GHN API

Module này chứa các hàm để thực hiện HTTP requests đến API GHN
"""

from typing import Dict, Optional, Any
from mcp.shared._httpx_utils import create_mcp_http_client
from .config import BASE_URL, DEFAULT_HEADERS

async def make_ghn_request(
    endpoint: str,
    method: str = "GET",
    data: Optional[Dict] = None,
    headers: Optional[Dict] = None
) -> Dict[str, Any]:
    """
    Thực hiện request đến API GHN
    
    Args:
        endpoint: Đường dẫn API endpoint
        method: HTTP method (GET, POST, PUT, DELETE)
        data: Dữ liệu gửi đi (cho POST/PUT)
        headers: Headers bổ sung
        
    Returns:
        Dict chứa response từ API GHN
        
    Raises:
        ValueError: Nếu HTTP method không được hỗ trợ
        HTTPError: <PERSON>ếu request thất bại
    """
    url = f"{BASE_URL}/{endpoint.lstrip('/')}"
    
    # Merge headers
    request_headers = DEFAULT_HEADERS.copy()
    if headers:
        request_headers.update(headers)
    
    # Tạo HTTP client
    async with create_mcp_http_client() as client:
        if method.upper() == "GET":
            response = await client.get(url, headers=request_headers, params=data)
        elif method.upper() == "POST":
            response = await client.post(url, headers=request_headers, json=data)
        elif method.upper() == "PUT":
            response = await client.put(url, headers=request_headers, json=data)
        elif method.upper() == "DELETE":
            response = await client.delete(url, headers=request_headers)
        else:
            raise ValueError(f"Phương thức HTTP không được hỗ trợ: {method}")
    
    response.raise_for_status()
    return response.json()
