"""
Script tạo dữ liệu mẫu cho RedAI MCP Server.

Script này tạo dữ liệu mẫu để test và phát triển.
"""

import asyncio
import json
from datetime import datetime

from .connection import get_db_session
from .repository import UserRepository, AgentRepository, ToolRepository, ResourceRepository


async def create_sample_users():
    """Tạo users mẫu."""

    users_data = [
        {
            "username": "admin",
            "email": "<EMAIL>",
            "full_name": "Administrator",
            "is_active": True,
            "api_key": "redai_admin_key_123456789"
        },
        {
            "username": "developer",
            "email": "<EMAIL>",
            "full_name": "Developer User",
            "is_active": True,
            "api_key": "redai_dev_key_987654321"
        },
        {
            "username": "tester",
            "email": "<EMAIL>",
            "full_name": "Test User",
            "is_active": True,
            "api_key": "redai_test_key_456789123"
        }
    ]

    created_users = []

    async with get_db_session() as session:
        user_repo = UserRepository(session)

        for user_data in users_data:
            # Ki<PERSON>m tra user đã tồn tại chưa
            existing_user = await user_repo.get_by_username(user_data["username"])
            if existing_user:
                print(f"  - User '{user_data['username']}' đã tồn tại, bỏ qua...")
                created_users.append(existing_user)
                continue

            user = await user_repo.create(**user_data)
            created_users.append(user)
            print(f"  - Đã tạo user: {user.username} ({user.email})")

    return created_users


async def create_sample_agents(users):
    """Tạo agents mẫu."""

    agents_data = [
        {
            "name": "ChatBot Assistant",
            "description": "AI chatbot để hỗ trợ khách hàng",
            "config": {
                "model": "gpt-4",
                "temperature": 0.7,
                "max_tokens": 2000
            },
            "is_active": True
        },
        {
            "name": "Code Generator",
            "description": "AI để tạo và review code",
            "config": {
                "model": "gpt-4",
                "temperature": 0.3,
                "max_tokens": 4000,
                "languages": ["python", "javascript", "typescript"]
            },
            "is_active": True
        },
        {
            "name": "Data Analyzer",
            "description": "AI để phân tích dữ liệu và tạo báo cáo",
            "config": {
                "model": "gpt-4",
                "temperature": 0.1,
                "max_tokens": 3000
            },
            "is_active": True
        }
    ]

    created_agents = []

    async with get_db_session() as session:
        agent_repo = AgentRepository(session)

        for i, agent_data in enumerate(agents_data):
            user = users[i % len(users)]  # Phân bổ agents cho các users
            agent_data["user_id"] = user.id

            # Kiểm tra agent đã tồn tại chưa
            existing_agent = await agent_repo.get_by_name(user.id, agent_data["name"])
            if existing_agent:
                print(f"  - Agent '{agent_data['name']}' của user '{user.username}' đã tồn tại, bỏ qua...")
                created_agents.append(existing_agent)
                continue

            agent = await agent_repo.create(**agent_data)
            created_agents.append(agent)
            print(f"  - Đã tạo agent: {agent.name} (user: {user.username})")

    return created_agents


async def create_sample_tools(agents):
    """Tạo tools mẫu."""

    tools_data = [
        {
            "name": "web_search",
            "description": "Tìm kiếm thông tin trên web",
            "tool_type": "integration",
            "config": {
                "api_url": "https://api.search.com",
                "timeout": 30
            },
            "input_schema": {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "Từ khóa tìm kiếm"},
                    "limit": {"type": "integer", "default": 10}
                },
                "required": ["query"]
            },
            "is_active": True
        },
        {
            "name": "calculate",
            "description": "Thực hiện các phép tính toán",
            "tool_type": "builtin",
            "config": {},
            "input_schema": {
                "type": "object",
                "properties": {
                    "expression": {"type": "string", "description": "Biểu thức toán học"}
                },
                "required": ["expression"]
            },
            "is_active": True
        },
        {
            "name": "send_email",
            "description": "Gửi email",
            "tool_type": "integration",
            "config": {
                "smtp_server": "smtp.gmail.com",
                "port": 587
            },
            "input_schema": {
                "type": "object",
                "properties": {
                    "to": {"type": "string", "description": "Địa chỉ email người nhận"},
                    "subject": {"type": "string", "description": "Tiêu đề email"},
                    "body": {"type": "string", "description": "Nội dung email"}
                },
                "required": ["to", "subject", "body"]
            },
            "is_active": True
        }
    ]

    created_tools = []

    async with get_db_session() as session:
        tool_repo = ToolRepository(session)

        for agent in agents:
            for tool_data in tools_data:
                tool_data_copy = tool_data.copy()
                tool_data_copy["agent_id"] = agent.id

                # Kiểm tra tool đã tồn tại chưa
                existing_tool = await tool_repo.get_by_name(agent.id, tool_data["name"])
                if existing_tool:
                    print(f"  - Tool '{tool_data['name']}' của agent '{agent.name}' đã tồn tại, bỏ qua...")
                    created_tools.append(existing_tool)
                    continue

                tool = await tool_repo.create(**tool_data_copy)
                created_tools.append(tool)
                print(f"  - Đã tạo tool: {tool.name} (agent: {agent.name})")

    return created_tools


async def create_sample_resources(agents):
    """Tạo resources mẫu."""

    resources_data = [
        {
            "uri": "file://docs/user_manual.pdf",
            "name": "User Manual",
            "description": "Hướng dẫn sử dụng hệ thống",
            "mime_type": "application/pdf",
            "content": "Nội dung hướng dẫn sử dụng...",
            "meta_data": {"version": "1.0", "language": "vi"},
            "is_active": True
        },
        {
            "uri": "http://api.example.com/schema",
            "name": "API Schema",
            "description": "Schema của API bên ngoài",
            "mime_type": "application/json",
            "content": json.dumps({"openapi": "3.0.0", "info": {"title": "Example API"}}),
            "meta_data": {"version": "3.0.0"},
            "is_active": True
        },
        {
            "uri": "db://config/settings",
            "name": "System Settings",
            "description": "Cấu hình hệ thống",
            "mime_type": "application/json",
            "content": json.dumps({"theme": "dark", "language": "vi"}),
            "meta_data": {"editable": True},
            "is_active": True
        }
    ]

    created_resources = []

    async with get_db_session() as session:
        resource_repo = ResourceRepository(session)

        for agent in agents:
            for resource_data in resources_data:
                resource_data_copy = resource_data.copy()
                resource_data_copy["agent_id"] = agent.id

                # Kiểm tra resource đã tồn tại chưa
                existing_resource = await resource_repo.get_by_uri(agent.id, resource_data["uri"])
                if existing_resource:
                    print(f"  - Resource '{resource_data['name']}' của agent '{agent.name}' đã tồn tại, bỏ qua...")
                    created_resources.append(existing_resource)
                    continue

                resource = await resource_repo.create(**resource_data_copy)
                created_resources.append(resource)
                print(f"  - Đã tạo resource: {resource.name} (agent: {agent.name})")

    return created_resources


async def main():
    """Hàm main của script."""

    print("=" * 60)
    print("🌱 TẠO DỮ LIỆU MẪU CHO REDAI MCP SERVER")
    print("=" * 60)

    try:
        print("\n👥 Đang tạo users mẫu...")
        users = await create_sample_users()
        print(f"✅ Đã tạo {len(users)} users")

        print("\n🤖 Đang tạo agents mẫu...")
        agents = await create_sample_agents(users)
        print(f"✅ Đã tạo {len(agents)} agents")

        print("\n🔧 Đang tạo tools mẫu...")
        tools = await create_sample_tools(agents)
        print(f"✅ Đã tạo {len(tools)} tools")

        print("\n📄 Đang tạo resources mẫu...")
        resources = await create_sample_resources(agents)
        print(f"✅ Đã tạo {len(resources)} resources")

        print("\n🎉 Tạo dữ liệu mẫu hoàn tất!")

    except Exception as e:
        print(f"\n❌ Lỗi khi tạo dữ liệu mẫu: {e}")
        import traceback
        traceback.print_exc()
        exit(1)


if __name__ == "__main__":
    asyncio.run(main())
