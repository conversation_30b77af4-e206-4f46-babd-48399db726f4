# Shipment MCP Server (GHN + GHTK)

Server MCP (Model Context Protocol) tích hợp với cả hai API vận chuyển hàng đầu Việt Nam:
- **GHN (Giao hàng <PERSON>hanh)**: <PERSON><PERSON><PERSON> đ<PERSON> h<PERSON>, t<PERSON><PERSON> cư<PERSON>h<PERSON>, quản lý địa chỉ và dịch vụ vận chuyển
- **GHTK (Giao hàng Tiết kiệm)**: <PERSON><PERSON><PERSON> đơn, t<PERSON><PERSON> ph<PERSON>, quản lý sản phẩm và địa chỉ

## 📁 Cấu trúc dự án

```
src/server/redai_system/
├── utils/                    # Tiện ích chung
│   ├── __init__.py
│   ├── config.py            # Cấu hình API GHN + GHTK
│   ├── api_client.py        # HTTP client GHN
│   └── ghtk_client.py       # HTTP client GHTK
├── tools/                   # Các công cụ (Tools)
│   ├── __init__.py
│   ├── order_tools.py       # Quản lý đơn hàng GHN
│   ├── calculation_tools.py # Tính toán phí và thời gian GHN
│   ├── support_tools.py     # Hỗ trợ khách hàng GHN
│   ├── management_tools.py  # Quản lý cửa hàng GHN
│   └── ghtk_tools.py        # Công cụ GHTK
├── resources/               # Các tài nguyên (Resources)
│   ├── __init__.py
│   ├── order_resources.py   # Thông tin đơn hàng GHN
│   ├── location_resources.py # Địa điểm GHN
│   ├── service_resources.py # Dịch vụ vận chuyển GHN
│   ├── support_resources.py # Hỗ trợ GHN
│   └── ghtk_resources.py    # Tài nguyên GHTK
├── docs/
│   └── api-GHTK.md         # Tài liệu API GHTK
├── shipment_server.py       # Server chính
├── test_shipment_server.py  # Test script
├── .env.example            # Cấu hình mẫu
├── README.md               # Tài liệu này
└── mcp_config.json         # Cấu hình MCP
```

## 🚀 Tính năng

### 🔧 GHN Tools (Công cụ GHN)
- **create_order** - Tạo đơn hàng mới
- **cancel_order** - Hủy đơn hàng
- **calculate_fee** - Tính cước phí vận chuyển
- **calculate_leadtime** - Tính thời gian giao hàng dự kiến
- **preview_order** - Xem trước đơn hàng (không tạo đơn thực)
- **create_print_token** - Tạo token để in vận đơn
- **create_ticket** - Tạo ticket hỗ trợ khách hàng
- **reply_ticket** - Phản hồi ticket hỗ trợ
- **get_otp** - Lấy mã OTP cho đăng ký
- **add_staff_to_store** - Thêm nhân viên vào cửa hàng

### 🔧 GHTK Tools (Công cụ GHTK)
- **ghtk_get_solutions_tool** - Lấy danh sách giải pháp (gói bảo hiểm/phụ phí)
- **ghtk_create_order_tool** - Tạo đơn hàng GHTK
- **ghtk_calculate_fee_tool** - Tính phí vận chuyển GHTK
- **ghtk_get_order_status_tool** - Lấy trạng thái đơn hàng
- **ghtk_print_label_tool** - In nhãn đơn hàng (PDF)
- **ghtk_cancel_order_tool** - Hủy đơn hàng
- **ghtk_get_pick_addresses_tool** - Lấy địa chỉ lấy hàng đã cấu hình
- **ghtk_get_level4_addresses_tool** - Lấy địa chỉ cấp 4 (tòa nhà, ngõ)
- **ghtk_search_products_tool** - Tìm kiếm sản phẩm đã tạo

### 📚 GHN Resources (Tài nguyên GHN)
- **ghn://order/{order_code}** - Thông tin chi tiết đơn hàng
- **ghn://order-by-client/{client_order_code}** - Đơn hàng theo mã khách hàng
- **ghn://provinces** - Danh sách tỉnh/thành phố
- **ghn://districts/{province_id}** - Danh sách quận/huyện theo tỉnh
- **ghn://wards/{district_id}** - Danh sách phường/xã theo quận
- **ghn://services/{from_district}/{to_district}** - Dịch vụ vận chuyển
- **ghn://stations/{district_id}** - Danh sách bưu cục GHN
- **ghn://stores** - Danh sách cửa hàng
- **ghn://pick-shifts** - Ca lấy hàng sắp tới
- **ghn://tickets** - Danh sách ticket hỗ trợ

### 📚 GHTK Resources (Tài nguyên GHTK)
- **ghtk://solutions** - Danh sách giải pháp (gói bảo hiểm/phụ phí)
- **ghtk://order/{tracking_order}** - Thông tin đơn hàng theo mã vận đơn
- **ghtk://pick-addresses** - Địa chỉ lấy hàng đã cấu hình
- **ghtk://products** - Danh sách tất cả sản phẩm
- **ghtk://products/search/{term}** - Tìm kiếm sản phẩm theo từ khóa
- **ghtk://webhook-info** - Thông tin về webhook GHTK
- **ghtk://status-codes** - Danh sách mã trạng thái đơn hàng GHTK

## ⚙️ Cài đặt

### 1. Cấu hình môi trường

```bash
# Sao chép file cấu hình mẫu
cp .env.example .env

# Chỉnh sửa file .env với thông tin thực tế
nano .env
```

### 2. Điền thông tin API

#### 2.1. Cấu hình GHN (Giao hàng Nhanh)

```env
# Token xác thực GHN
GHN_TOKEN=your_ghn_token_here

# ID cửa hàng GHN
GHN_SHOP_ID=your_shop_id_here

# Môi trường (test/production)
GHN_ENVIRONMENT=test
```

#### 2.2. Cấu hình GHTK (Giao hàng Tiết kiệm)

```env
# Token xác thực GHTK
GHTK_TOKEN=your_ghtk_token_here

# Mã đối tác GHTK
GHTK_PARTNER_CODE=your_partner_code_here

# Môi trường (test/production)
GHTK_ENVIRONMENT=test
```

### 3. Lấy thông tin API

#### 3.1. Lấy thông tin GHN

1. **Đăng ký tài khoản GHN:**
   - Test: https://dev-online-gateway.ghn.vn
   - Production: https://online-gateway.ghn.vn

2. **Tạo cửa hàng:**
   - Đăng nhập vào trang quản lý
   - Tạo cửa hàng mới
   - Lấy Token và ShopID từ trang quản lý

#### 3.2. Lấy thông tin GHTK

1. **Đăng ký tài khoản GHTK:**
   - Website: https://ghtk.vn
   - Đăng ký tài khoản đối tác

2. **Lấy thông tin API:**
   - Đăng nhập vào trang quản lý đối tác
   - Vào phần API/Tích hợp
   - Lấy Token và Partner Code

3. **Cấu hình webhook (tùy chọn):**
   - Cấu hình URL webhook để nhận thông báo cập nhật trạng thái
   - URL webhook phải trả về HTTP 200

## 🚀 Chạy Server

### ⚠️ **Lưu ý về Import Error**

Nếu gặp lỗi `ImportError: attempted relative import with no known parent package`, hãy sử dụng một trong các cách sau:

### **Cách 1: Sử dụng launcher script (Đơn giản nhất)**
```bash
# Từ bất kỳ thư mục nào
python src/server/redai_system/run_server.py
```

### **Cách 2: Chạy như module (Khuyến nghị)**
```bash
# Từ thư mục gốc dự án
python -m src.server.redai_system.shipment_server

# Hoặc từ thư mục src
cd src
python -m server.redai_system.shipment_server
```

### **Cách 3: Chạy trực tiếp (Đã sửa imports)**
```bash
# FastMCP với Streamable HTTP transport (đã sửa imports)
python src/server/redai_system/shipment_server.py

# Low-level SSE server với cấu hình chi tiết (deprecated)
python src/server/redai_system/shipment_server_sse.py

# Stdio transport cho development
python src/server/redai_system/shipment_server_stdio.py
```

### **Cách 4: Chạy với MCP CLI**
```bash
# Development mode (chỉ hỗ trợ stdio)
mcp dev src/server/redai_system/shipment_server.py

# Install vào Claude Desktop
mcp install src/server/redai_system/shipment_server.py --name "GHN Shipment"
```

## 🧪 Testing và Debug

### **Kiểm tra imports và cấu hình**
```bash
# Test tất cả imports
python src/server/redai_system/test_imports.py

# Test tổng hợp tất cả
python src/server/redai_system/run_all_tests.py

# Test server features
python src/server/redai_system/test_shipment_server.py
```

### **Troubleshooting Import Errors**

**Lỗi**: `ImportError: attempted relative import with no known parent package`

**Nguyên nhân**: File sử dụng relative imports (`from .utils import ...`) nhưng được chạy trực tiếp

**Giải pháp**:
1. ✅ **Sử dụng launcher**: `python run_server.py`
2. ✅ **Chạy như module**: `python -m src.server.redai_system.shipment_server`
3. ✅ **File đã được sửa**: Có fallback cho absolute imports

**Lỗi**: `ModuleNotFoundError: No module named 'utils'`

**Giải pháp**: Đảm bảo chạy từ đúng thư mục hoặc sử dụng launcher script

### Kết nối với HTTP Client
```python
# Ví dụ kết nối từ client với Streamable HTTP
from fastmcp import FastMCPClient

# Kết nối đến server
client = FastMCPClient("http://localhost:8000/mcp")

# Sử dụng client
result = await client.call_tool("create_order", {...})
```

### Kết nối với SSE Client (Deprecated)
```python
# Ví dụ kết nối từ client với SSE (deprecated)
from mcp.client.sse import sse_client
from mcp import ClientSession

async with sse_client("http://localhost:8000/sse") as (read, write):
    async with ClientSession(read, write) as session:
        await session.initialize()
        # Sử dụng session...
```

## 📖 Ví dụ sử dụng

### GHN - Tạo đơn hàng mới
```python
# Sử dụng tool create_order
{
  "to_name": "Nguyễn Văn A",
  "to_phone": "0912345678",
  "to_address": "123 Đường ABC, Phường XYZ",
  "to_ward_code": "20101",
  "to_district_id": 1442,
  "cod_amount": 500000,
  "content": "Quần áo",
  "weight": 1000,
  "length": 30,
  "width": 20,
  "height": 10,
  "service_type_id": 2
}
```

### GHN - Tính cước phí vận chuyển
```python
# Sử dụng tool calculate_fee
{
  "from_district_id": 1447,
  "to_district_id": 1442,
  "to_ward_code": "20101",
  "weight": 1000,
  "service_type_id": 2
}
```

### GHTK - Tạo đơn hàng mới
```python
# Sử dụng tool ghtk_create_order_tool
{
  "products": [
    {
      "name": "Áo thun",
      "weight": 0.3,
      "quantity": 2,
      "price": 200000
    }
  ],
  "order_info": {
    "id": "ORDER123",
    "pick_name": "Kho HCM",
    "pick_address": "123 Nguyễn Văn Cừ",
    "pick_province": "TP. Hồ Chí Minh",
    "pick_district": "Quận 1",
    "pick_ward": "Phường Bến Nghé",
    "pick_tel": "0901234567",
    "name": "Nguyễn Văn A",
    "address": "456 Lê Lợi",
    "province": "TP. Hồ Chí Minh",
    "district": "Quận 3",
    "ward": "Phường 1",
    "tel": "0912345678",
    "value": 400000
  }
}
```

### GHTK - Tính phí vận chuyển
```python
# Sử dụng tool ghtk_calculate_fee_tool
{
  "pick_province": "TP. Hồ Chí Minh",
  "pick_district": "Quận 1",
  "province": "TP. Hồ Chí Minh",
  "district": "Quận 3",
  "weight": 500
}
```

### Lấy thông tin đơn hàng
```python
# GHN: Sử dụng resource ghn://order/{order_code}
# Ví dụ: ghn://order/5F5NH3LN

# GHTK: Sử dụng resource ghtk://order/{tracking_order}
# Ví dụ: ghtk://order/S1.A1.2001297581
```

### Lấy danh sách địa chỉ
```python
# GHN: Sử dụng resource ghn://provinces
# GHTK: Sử dụng resource ghtk://pick-addresses
```

## 🔒 Bảo mật

### GHN
- Xác thực qua Token và ShopID của GHN
- Hỗ trợ cả môi trường test và production
- Tất cả API calls được mã hóa HTTPS

### GHTK
- Xác thực qua Token và Partner Code của GHTK
- Hỗ trợ cả môi trường test và production
- Tất cả API calls được mã hóa HTTPS
- Hỗ trợ webhook để nhận thông báo cập nhật trạng thái

## 📝 Ghi chú

- Server sử dụng FastMCP framework
- Transport mặc định: **Streamable HTTP** (khuyến nghị cho production)
- Transport khác: SSE (deprecated), Stdio (development)
- Tất cả response trả về định dạng JSON
- Hỗ trợ đầy đủ API GHN v2, GHTK, J&T Express và Ahamove
- Có thể sử dụng một hoặc tất cả API cùng lúc
- API Documentation tự động tại `/docs` endpoint

## 🐛 Xử lý lỗi

Server sẽ trả về thông báo lỗi chi tiết khi:
- Thiếu thông tin xác thực (GHN hoặc GHTK)
- Tham số không hợp lệ
- Lỗi kết nối API
- Lỗi xử lý dữ liệu
- Lỗi định dạng JSON

## 📞 Hỗ trợ

### Tài liệu API
- **GHN**: https://api.ghn.vn/home/<USER>/detail?id=78
- **GHTK**: https://api.ghtk.vn/docs/
- **GHTK OpenAPI**: Xem file `docs/api-GHTK.md` trong dự án

### Webhook GHTK
- Sử dụng resource `ghtk://webhook-info` để xem thông tin chi tiết
- Sử dụng resource `ghtk://status-codes` để xem danh sách mã trạng thái
