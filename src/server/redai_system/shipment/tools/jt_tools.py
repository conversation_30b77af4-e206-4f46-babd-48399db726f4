"""
J&T Express Tools cho Shipment MCP Server

Mo<PERSON><PERSON> <PERSON><PERSON><PERSON> chứa các tools để tương tác với J&T Express API
"""

import json
from datetime import datetime, timedelta
from mcp.server.fastmcp import FastMCP

# Import với fallback
try:
    from src.server.redai_system.shipment.utils.jt_client import (
        jt_create_order,
        jt_track_order,
        jt_calculate_tariff,
        jt_cancel_order,
        create_jt_order_data
    )
except ImportError:
    from utils.jt_client import (
        jt_create_order,
        jt_track_order,
        jt_calculate_tariff,
        jt_cancel_order,
        create_jt_order_data
    )

def register_jt_tools(mcp: FastMCP):
    """Đăng ký tất cả J&T Express tools với MCP server"""

    @mcp.tool()
    async def jt_create_order_tool(
        order_id: str,
        shipper_name: str,
        shipper_phone: str,
        shipper_address: str,
        origin_code: str,
        receiver_name: str,
        receiver_phone: str,
        receiver_address: str,
        receiver_zipcode: str,
        destination_code: str,
        receiver_area: str,
        quantity: int,
        weight: float,
        goods_description: str,
        item_name: str,
        goods_value: int = 0,
        service_type: int = 1,
        insurance: int = 0,
        cod: int = 0,
        pickup_hours_from_now: int = 2
    ) -> str:
        """
        Tạo đơn hàng J&T Express
        
        Args:
            order_id: Mã đơn hàng (tối đa 20 ký tự, nên có prefix riêng)
            shipper_name: Tên người gửi (tối đa 30 ký tự)
            shipper_phone: Số điện thoại người gửi (format +62xxxxxxxxxx)
            shipper_address: Địa chỉ người gửi (tối đa 200 ký tự)
            origin_code: Mã thành phố gửi (3 ký tự viết hoa, ví dụ: JKT)
            receiver_name: Tên người nhận (tối đa 30 ký tự)
            receiver_phone: Số điện thoại người nhận (format +62xxxxxxxxxx)
            receiver_address: Địa chỉ người nhận (tối đa 200 ký tự)
            receiver_zipcode: Mã bưu điện người nhận (5 ký tự, có thể "00000")
            destination_code: Mã thành phố đích (3 ký tự viết hoa)
            receiver_area: Mã quận/huyện đích (ví dụ: JKT001)
            quantity: Số lượng mặt hàng trong gói
            weight: Trọng lượng gói (kg, có thể có 2 chữ số thập phân)
            goods_description: Mô tả hàng hóa (tối đa 40 ký tự, không ký tự đặc biệt)
            item_name: Tên mặt hàng (tối đa 50 ký tự)
            goods_value: Giá trị hàng hóa (tối đa 8 chữ số)
            service_type: Loại dịch vụ (1=Pickup, 6=Drop-off)
            insurance: Giá trị bảo hiểm (nếu có)
            cod: Giá trị thu hộ COD (tối đa 8 chữ số)
            pickup_hours_from_now: Số giờ từ bây giờ để lấy hàng (mặc định 2 giờ)
        
        Returns:
            JSON string chứa thông tin đơn hàng đã tạo
        """
        try:
            # Tạo thời gian đặt đơn và khung giờ lấy hàng
            now = datetime.now()
            order_date = now.strftime("%Y-%m-%d %H:%M:%S")
            
            pickup_start = now + timedelta(hours=pickup_hours_from_now)
            pickup_end = pickup_start + timedelta(hours=4)  # Khung giờ 4 tiếng
            
            pickup_start_time = pickup_start.strftime("%Y-%m-%d %H:%M:%S")
            pickup_end_time = pickup_end.strftime("%Y-%m-%d %H:%M:%S")
            
            # Chuẩn bị dữ liệu đơn hàng
            shipper_info = {
                "name": shipper_name,
                "contact": shipper_name,
                "phone": shipper_phone,
                "address": shipper_address,
                "city_code": origin_code
            }
            
            receiver_info = {
                "name": receiver_name,
                "phone": receiver_phone,
                "address": receiver_address,
                "zipcode": receiver_zipcode,
                "city_code": destination_code,
                "area_code": receiver_area
            }
            
            package_info = {
                "quantity": quantity,
                "weight": weight,
                "description": goods_description,
                "item_name": item_name,
                "value": goods_value
            }
            
            service_info = {
                "service_type": service_type,
                "insurance": insurance,
                "cod": cod,
                "order_date": order_date,
                "pickup_start_time": pickup_start_time,
                "pickup_end_time": pickup_end_time
            }
            
            # Tạo dữ liệu đơn hàng
            order_data = create_jt_order_data(
                order_id, shipper_info, receiver_info, package_info, service_info
            )
            
            # Gọi API tạo đơn
            result = await jt_create_order(order_data)
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            return f"Lỗi khi tạo đơn hàng J&T: {str(e)}"

    @mcp.tool()
    async def jt_track_order_tool(awb: str, eccompanyid: str = "") -> str:
        """
        Tra cứu trạng thái đơn hàng J&T Express
        
        Args:
            awb: Mã vận đơn (AWB) J&T
            eccompanyid: Mã cửa hàng/đối tác (để trống sẽ dùng mặc định)
        
        Returns:
            JSON string chứa thông tin chi tiết đơn hàng và lịch sử vận chuyển
        """
        try:
            result = await jt_track_order(awb, eccompanyid if eccompanyid else None)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi tra cứu đơn hàng J&T: {str(e)}"

    @mcp.tool()
    async def jt_calculate_tariff_tool(
        weight: float,
        send_site_code: str,
        dest_area_code: str,
        product_type: str = "EZ"
    ) -> str:
        """
        Tính cước vận chuyển J&T Express
        
        Args:
            weight: Trọng lượng gói hàng (kg)
            send_site_code: Mã tỉnh/thành gốc (viết hoa, ví dụ: JAKARTA)
            dest_area_code: Mã quận/huyện đích (viết hoa, ví dụ: KALIDERES)
            product_type: Loại sản phẩm (mặc định "EZ")
        
        Returns:
            JSON string chứa thông tin cước phí
        """
        try:
            result = await jt_calculate_tariff(weight, send_site_code, dest_area_code, product_type)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi tính cước vận chuyển J&T: {str(e)}"

    @mcp.tool()
    async def jt_cancel_order_tool(order_id: str, remark: str = "Canceled by system") -> str:
        """
        Hủy đơn hàng J&T Express
        
        Args:
            order_id: Mã đơn hàng cần hủy
            remark: Lý do hủy đơn (tối đa 30 ký tự)
        
        Returns:
            JSON string chứa kết quả hủy đơn
        """
        try:
            result = await jt_cancel_order(order_id, remark)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi hủy đơn hàng J&T: {str(e)}"

    @mcp.tool()
    async def jt_create_simple_order_tool(
        order_id: str,
        from_city: str,
        to_city: str,
        to_area: str,
        receiver_name: str,
        receiver_phone: str,
        receiver_address: str,
        weight: float,
        item_description: str,
        cod_amount: int = 0
    ) -> str:
        """
        Tạo đơn hàng J&T Express đơn giản (với thông tin người gửi mặc định)
        
        Args:
            order_id: Mã đơn hàng (tối đa 20 ký tự)
            from_city: Mã thành phố gửi (3 ký tự viết hoa, ví dụ: JKT)
            to_city: Mã thành phố đích (3 ký tự viết hoa)
            to_area: Mã quận/huyện đích (ví dụ: JKT001)
            receiver_name: Tên người nhận
            receiver_phone: Số điện thoại người nhận (format +62xxxxxxxxxx)
            receiver_address: Địa chỉ người nhận
            weight: Trọng lượng gói (kg)
            item_description: Mô tả hàng hóa
            cod_amount: Số tiền thu hộ (nếu có)
        
        Returns:
            JSON string chứa thông tin đơn hàng đã tạo
        """
        try:
            # Sử dụng thông tin người gửi mặc định
            return await jt_create_order_tool(
                order_id=order_id,
                shipper_name="Default Shipper",
                shipper_phone="+6281234567890",
                shipper_address="Default Address",
                origin_code=from_city,
                receiver_name=receiver_name,
                receiver_phone=receiver_phone,
                receiver_address=receiver_address,
                receiver_zipcode="00000",
                destination_code=to_city,
                receiver_area=to_area,
                quantity=1,
                weight=weight,
                goods_description=item_description[:40],  # Giới hạn 40 ký tự
                item_name=item_description[:50],  # Giới hạn 50 ký tự
                cod=cod_amount
            )
        except Exception as e:
            return f"Lỗi khi tạo đơn hàng J&T đơn giản: {str(e)}"

    @mcp.tool()
    async def jt_get_service_info_tool() -> str:
        """
        Lấy thông tin về các dịch vụ và mã vùng J&T Express
        
        Returns:
            JSON string chứa thông tin hướng dẫn sử dụng J&T API
        """
        service_info = {
            "service_types": {
                "1": "Pickup Service - J&T sẽ đến lấy hàng tại địa chỉ người gửi",
                "6": "Drop-off Service - Người gửi mang hàng đến điểm J&T"
            },
            "express_types": {
                "1": "EZ - Giao hàng thường (duy nhất hiện tại)"
            },
            "phone_format": {
                "description": "Số điện thoại phải có format +62xxxxxxxxxx (Indonesia)",
                "example": "+6281234567890"
            },
            "city_codes": {
                "description": "Mã thành phố 3 ký tự viết hoa",
                "examples": {
                    "JKT": "Jakarta",
                    "BDG": "Bandung", 
                    "SBY": "Surabaya",
                    "MDN": "Medan",
                    "DPS": "Denpasar"
                }
            },
            "area_codes": {
                "description": "Mã quận/huyện theo format CITY + số",
                "examples": {
                    "JKT001": "Jakarta area 001",
                    "BDG001": "Bandung area 001"
                }
            },
            "limitations": {
                "order_id": "Tối đa 20 ký tự, nên có prefix riêng",
                "shipper_name": "Tối đa 30 ký tự",
                "receiver_name": "Tối đa 30 ký tự",
                "shipper_address": "Tối đa 200 ký tự",
                "receiver_address": "Tối đa 200 ký tự",
                "goods_description": "Tối đa 40 ký tự, không ký tự đặc biệt",
                "item_name": "Tối đa 50 ký tự",
                "remark": "Tối đa 30 ký tự (cho hủy đơn)",
                "goods_value": "Tối đa 8 chữ số",
                "cod": "Tối đa 8 chữ số"
            },
            "notes": [
                "J&T Express chủ yếu hoạt động tại Indonesia",
                "Tất cả thời gian sử dụng múi giờ UTC+7",
                "API yêu cầu chữ ký bảo mật cho mọi request",
                "Tracking API sử dụng Basic Authentication",
                "Zipcode có thể để '00000' nếu không biết chính xác"
            ]
        }
        
        return json.dumps(service_info, ensure_ascii=False, indent=2)
