{"mcpServers": {"redai-data-module": {"url": "http://127.0.0.1:8002/mcp", "name": "RedAI Data Module Server", "description": "MCP Server cho Data Module API - Quản lý Media, URL và Statistics", "version": "1.0.0", "transport": "streamable-http", "timeout": 30000, "retries": 3, "capabilities": {"tools": true, "resources": false, "prompts": false, "logging": true}, "authentication": {"type": "bearer", "required": false, "description": "Bearer token sẽ đư<PERSON>c cấu hình từ client thông qua tool update_bearer_token"}, "endpoints": {"base": "http://127.0.0.1:8002", "mcp": "http://127.0.0.1:8002/mcp", "health": "http://127.0.0.1:8002/health"}, "tools": [{"name": "delete_media_my-media", "description": "<PERSON><PERSON><PERSON> mềm nhiều media thuộc sở hữu của người dùng hiện tại", "category": "media", "tags": ["User Media"]}, {"name": "post_data_url", "description": "Tạo một URL mới cho người dùng hiện tại", "category": "url", "tags": ["User URL"]}, {"name": "put_data_url", "description": "<PERSON><PERSON><PERSON> nhật thông tin của một URL cụ thể", "category": "url", "tags": ["User URL"]}, {"name": "delete_data_url", "description": "Xóa một URL cụ thể thuộc sở hữu của người dùng hiện tại", "category": "url", "tags": ["User URL"]}, {"name": "update_bearer_token", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t Bearer token cho authentication", "category": "auth", "tags": ["Authentication"]}, {"name": "check_auth_status", "description": "<PERSON><PERSON><PERSON> tra trạng thái authentication hiện tại", "category": "auth", "tags": ["Authentication"]}], "api_info": {"title": "Data Module API", "description": "API documentation for Data Module - <PERSON><PERSON><PERSON><PERSON> lý dữ liệu trong hệ thống bao gồm Media, URL, Knowledge Files và Statistics", "version": "1.0.0", "base_url": "https://api.redai.com", "endpoints_count": 5}, "usage_examples": {"authentication": {"step1": "Gọi tool update_bearer_token với bearer_token", "step2": "Gọi tool check_auth_status để xác nhận", "step3": "Sử dụng các API tools khác"}, "media_management": {"delete_media": "Sử dụng delete_media_my-media với mediaIds array"}, "url_management": {"create_url": "Sử dụng post_data_url với url, title, content", "update_url": "Sử dụng put_data_url với id và thông tin cập nhật", "delete_url": "Sử dụng delete_data_url với id"}}}}, "client_config": {"default_timeout": 30000, "retry_attempts": 3, "retry_delay": 1000, "connection_pool_size": 10, "keep_alive": true, "user_agent": "FastMCP-Client/2.0", "headers": {"Content-Type": "application/json", "Accept": "application/json"}}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "mcp_client.log", "console": true}, "environment": {"development": {"server_url": "http://127.0.0.1:8002/mcp", "debug": true, "log_level": "DEBUG"}, "production": {"server_url": "https://api.redai.com/mcp", "debug": false, "log_level": "INFO", "ssl_verify": true}}, "metadata": {"created": "2025-01-16", "version": "1.0.0", "author": "RedAI Development Team", "description": "<PERSON><PERSON><PERSON> hình kết nối MCP cho RedAI Data Module Server", "last_updated": "2025-01-16T11:06:00Z", "schema_version": "1.0"}}