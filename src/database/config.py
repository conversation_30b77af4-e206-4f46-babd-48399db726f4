"""
Cấ<PERSON> hình database cho RedAI MCP Server.

Module n<PERSON><PERSON> đọc cấu hình database từ environment variables và tạo URL kết nối.
"""

import os
from typing import Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class DatabaseConfig:
    """Cấu hình database từ environment variables."""
    
    host: str
    port: int
    username: str
    password: str
    database: str
    ssl: bool = True
    
    # Connection pool settings
    pool_size: int = 5
    max_overflow: int = 10
    pool_timeout: int = 30
    pool_recycle: int = 1800
    echo: bool = False
    
    @classmethod
    def from_env(cls) -> "DatabaseConfig":
        """Tạo cấu hình database từ environment variables."""
        return cls(
            host=os.getenv("DB_HOST", "localhost"),
            port=int(os.getenv("DB_PORT", "5432")),
            username=os.getenv("DB_USERNAME", "postgres"),
            password=os.getenv("DB_PASSWORD", ""),
            database=os.getenv("DB_DATABASE", "postgres"),
            ssl=os.getenv("DB_SSL", "true").lower() == "true",
            pool_size=int(os.getenv("DB_POOL_SIZE", "5")),
            max_overflow=int(os.getenv("DB_MAX_OVERFLOW", "10")),
            pool_timeout=int(os.getenv("DB_POOL_TIMEOUT", "30")),
            pool_recycle=int(os.getenv("DB_POOL_RECYCLE", "1800")),
            echo=os.getenv("DB_ECHO", "false").lower() == "true"
        )
    
    def get_database_url(self) -> str:
        """Tạo URL kết nối database."""
        ssl_mode = "require" if self.ssl else "disable"
        return (
            f"postgresql://{self.username}:{self.password}"
            f"@{self.host}:{self.port}/{self.database}"
            f"?sslmode={ssl_mode}"
        )
    
    def get_async_database_url(self) -> str:
        """Tạo URL kết nối database bất đồng bộ."""
        ssl_mode = "require" if self.ssl else "disable"
        return (
            f"postgresql+asyncpg://{self.username}:{self.password}"
            f"@{self.host}:{self.port}/{self.database}"
            f"?ssl={ssl_mode}"
        )


# Tạo instance cấu hình global
db_config = DatabaseConfig.from_env()

# Export URL để sử dụng dễ dàng
DATABASE_URL = db_config.get_database_url()
ASYNC_DATABASE_URL = db_config.get_async_database_url()
