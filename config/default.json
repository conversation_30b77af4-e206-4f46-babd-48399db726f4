{"server": {"api_base_url": "http://localhost:3003", "mcp": {"name": "RedAI MCP Server", "transport": "stdio", "host": "127.0.0.1", "port": 8000, "path": "/mcp"}, "redai_global": {"name": "RedAI Global MCP Server", "transport": "streamable-http", "host": "127.0.0.1", "port": 8001, "path": "/mcp/global"}, "redai_system": {"name": "RedAI System MCP Server", "transport": "streamable-http", "host": "127.0.0.1", "port": 8002, "path": "/mcp/system"}}, "database": {"driver": "postgresql", "host": "localhost", "port": 5432, "username": "postgres", "password": "postgres", "database": "redai_mcp", "pool_size": 5, "max_overflow": 10, "pool_timeout": 30, "pool_recycle": 1800, "echo": false}}