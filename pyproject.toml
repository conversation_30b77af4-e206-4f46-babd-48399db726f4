[project]
name = "redai-v201-mcp-server"
version = "0.1.0"
description = "RedAI MCP Server with database integration"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastmcp>=2.8.0",
    "httpx>=0.24.0",           # HTTP client cho API calls
    "sqlalchemy>=2.0.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",    # <PERSON><PERSON><PERSON><PERSON><PERSON> môi trường từ file .env
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.18.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "mypy>=1.0.0",
]

[tool.black]
line-length = 100
target-version = ["py310", "py311", "py312", "py313"]

[tool.isort]
profile = "black"
line_length = 100
