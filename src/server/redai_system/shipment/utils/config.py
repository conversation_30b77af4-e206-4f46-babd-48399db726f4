"""
Cấu hình chung cho Shipment MCP Server (GHN + GHTK)

Module này chứa tất cả các cấu hình và constants cho server
"""

import os
from enum import Enum
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class ShipmentEnvironment(Enum):
    """Môi trường API"""
    TEST = "test"
    PRODUCTION = "production"

# ============================================================================
# GHN Configuration
# ============================================================================

# Lấy cấu hình GHN từ environment variables
GHN_TOKEN = os.getenv("GHN_TOKEN")
GHN_SHOP_ID = os.getenv("GHN_SHOP_ID")
GHN_ENVIRONMENT = os.getenv("GHN_ENVIRONMENT", "test")

# Sử dụng giá trị mặc định nếu chưa cấu hình (cho testing)
if not GHN_TOKEN:
    print("⚠️  GHN_TOKEN chưa được cấu hình, sử dụng giá trị test")
    GHN_TOKEN = "test_token_12345"

if not GHN_SHOP_ID:
    print("⚠️  GHN_SHOP_ID chưa được cấu hình, sử dụng giá trị test")
    GHN_SHOP_ID = "12345"

# Base URLs cho GHN
GHN_BASE_URLS = {
    ShipmentEnvironment.TEST.value: "https://dev-online-gateway.ghn.vn",
    ShipmentEnvironment.PRODUCTION.value: "https://online-gateway.ghn.vn"
}

GHN_BASE_URL = GHN_BASE_URLS.get(GHN_ENVIRONMENT, GHN_BASE_URLS[ShipmentEnvironment.TEST.value])

# Headers mặc định cho API GHN
GHN_DEFAULT_HEADERS = {
    "Token": GHN_TOKEN,
    "ShopId": GHN_SHOP_ID,
    "Content-Type": "application/json"
}

# ============================================================================
# GHTK Configuration
# ============================================================================

# Lấy cấu hình GHTK từ environment variables
GHTK_TOKEN = os.getenv("GHTK_TOKEN")
GHTK_PARTNER_CODE = os.getenv("GHTK_PARTNER_CODE")
GHTK_ENVIRONMENT = os.getenv("GHTK_ENVIRONMENT", "test")

# Sử dụng giá trị mặc định nếu chưa cấu hình (cho testing)
if not GHTK_TOKEN:
    print("⚠️  GHTK_TOKEN chưa được cấu hình, sử dụng giá trị test")
    GHTK_TOKEN = "test_ghtk_token_12345"

if not GHTK_PARTNER_CODE:
    print("⚠️  GHTK_PARTNER_CODE chưa được cấu hình, sử dụng giá trị test")
    GHTK_PARTNER_CODE = "test_partner_12345"

# Base URLs cho GHTK
GHTK_BASE_URLS = {
    ShipmentEnvironment.TEST.value: "https://services.ghtk.vn",
    ShipmentEnvironment.PRODUCTION.value: "https://services.ghtk.vn"
}

GHTK_BASE_URL = GHTK_BASE_URLS.get(GHTK_ENVIRONMENT, GHTK_BASE_URLS[ShipmentEnvironment.TEST.value])

# Headers mặc định cho API GHTK
GHTK_DEFAULT_HEADERS = {
    "Token": GHTK_TOKEN,
    "X-Client-Source": GHTK_PARTNER_CODE,
    "Content-Type": "application/json"
}

# ============================================================================
# J&T Configuration
# ============================================================================

# Lấy cấu hình J&T từ environment variables
JT_USERNAME = os.getenv("JT_USERNAME")
JT_API_KEY = os.getenv("JT_API_KEY")
JT_CUSTOMER_CODE = os.getenv("JT_CUSTOMER_CODE")
JT_ENVIRONMENT = os.getenv("JT_ENVIRONMENT", "test")

# Sử dụng giá trị mặc định nếu chưa cấu hình (cho testing)
if not JT_USERNAME:
    print("⚠️  JT_USERNAME chưa được cấu hình, sử dụng giá trị test")
    JT_USERNAME = "test_jt_username"

if not JT_API_KEY:
    print("⚠️  JT_API_KEY chưa được cấu hình, sử dụng giá trị test")
    JT_API_KEY = "test_jt_api_key_12345"

if not JT_CUSTOMER_CODE:
    print("⚠️  JT_CUSTOMER_CODE chưa được cấu hình, sử dụng giá trị test")
    JT_CUSTOMER_CODE = "test_customer_12345"

# Base URLs cho J&T (Indonesia-based)
JT_BASE_URLS = {
    ShipmentEnvironment.TEST.value: "https://developer.jet.co.id/api",
    ShipmentEnvironment.PRODUCTION.value: "https://api.jet.co.id"
}

JT_BASE_URL = JT_BASE_URLS.get(JT_ENVIRONMENT, JT_BASE_URLS[ShipmentEnvironment.TEST.value])

# Headers mặc định cho API J&T
JT_DEFAULT_HEADERS = {
    "Content-Type": "application/x-www-form-urlencoded",
    "User-Agent": "MCP-Shipment-Server/1.0"
}

# ============================================================================
# Ahamove Configuration
# ============================================================================

# Lấy cấu hình Ahamove từ environment variables
AHAMOVE_API_KEY = os.getenv("AHAMOVE_API_KEY")
AHAMOVE_TOKEN = os.getenv("AHAMOVE_TOKEN")
AHAMOVE_MOBILE = os.getenv("AHAMOVE_MOBILE")
AHAMOVE_ENVIRONMENT = os.getenv("AHAMOVE_ENVIRONMENT", "test")

# Sử dụng giá trị mặc định nếu chưa cấu hình (cho testing)
if not AHAMOVE_API_KEY:
    print("⚠️  AHAMOVE_API_KEY chưa được cấu hình, sử dụng giá trị test")
    AHAMOVE_API_KEY = "test_ahamove_api_key_12345"

if not AHAMOVE_TOKEN:
    print("⚠️  AHAMOVE_TOKEN chưa được cấu hình, sử dụng giá trị test")
    AHAMOVE_TOKEN = "test_ahamove_token_12345"

if not AHAMOVE_MOBILE:
    print("⚠️  AHAMOVE_MOBILE chưa được cấu hình, sử dụng giá trị test")
    AHAMOVE_MOBILE = "84912345678"

# Base URLs cho Ahamove
AHAMOVE_BASE_URLS = {
    ShipmentEnvironment.TEST.value: "https://partner-apistg.ahamove.com/v3",
    ShipmentEnvironment.PRODUCTION.value: "https://partner-api.ahamove.com/v3"
}

AHAMOVE_BASE_URL = AHAMOVE_BASE_URLS.get(AHAMOVE_ENVIRONMENT, AHAMOVE_BASE_URLS[ShipmentEnvironment.TEST.value])

# Headers mặc định cho API Ahamove
AHAMOVE_DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "User-Agent": "MCP-Shipment-Server/1.0"
}

# ============================================================================
# Backward Compatibility (cho code cũ)
# ============================================================================

# Giữ lại các biến cũ để không phá vỡ code hiện tại
BASE_URL = GHN_BASE_URL
DEFAULT_HEADERS = GHN_DEFAULT_HEADERS
