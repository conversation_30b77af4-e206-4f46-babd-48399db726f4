"""
<PERSON><PERSON><PERSON> truy vấn SQL thô cho RedAI MCP Server.

Module này cung cấp các truy vấn SQL phức tạp và báo cáo.
"""

from typing import List, Dict, Any, Optional
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from .connection import get_db_session


class DatabaseQueries:
    """Class chứa các truy vấn SQL thô."""
    
    @staticmethod
    async def get_database_stats() -> Dict[str, Any]:
        """Lấy thống kê tổng quan về database."""
        
        async with get_db_session() as session:
            # Thống kê số lượng records
            stats_query = text("""
                SELECT 
                    'users' as table_name, COUNT(*) as count FROM users
                UNION ALL
                SELECT 
                    'agents' as table_name, COUNT(*) as count FROM agents
                UNION ALL
                SELECT 
                    'tools' as table_name, COUNT(*) as count FROM tools
                UNION ALL
                SELECT 
                    'resources' as table_name, COUNT(*) as count FROM resources
            """)
            
            result = await session.execute(stats_query)
            stats = {row.table_name: row.count for row in result}
            
            # Thống kê dung lượng bảng
            size_query = text("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
            """)
            
            result = await session.execute(size_query)
            table_sizes = [
                {
                    "table": row.tablename,
                    "size": row.size,
                    "size_bytes": row.size_bytes
                }
                for row in result
            ]
            
            return {
                "record_counts": stats,
                "table_sizes": table_sizes,
                "total_tables": len(table_sizes)
            }
    
    @staticmethod
    async def get_user_activity_report() -> List[Dict[str, Any]]:
        """Báo cáo hoạt động của users."""
        
        async with get_db_session() as session:
            query = text("""
                SELECT 
                    u.id,
                    u.username,
                    u.email,
                    u.full_name,
                    u.is_active,
                    u.created_at,
                    COUNT(DISTINCT a.id) as agent_count,
                    COUNT(DISTINCT t.id) as tool_count,
                    COUNT(DISTINCT r.id) as resource_count,
                    MAX(a.updated_at) as last_agent_update
                FROM users u
                LEFT JOIN agents a ON u.id = a.user_id
                LEFT JOIN tools t ON a.id = t.agent_id
                LEFT JOIN resources r ON a.id = r.agent_id
                GROUP BY u.id, u.username, u.email, u.full_name, u.is_active, u.created_at
                ORDER BY u.created_at DESC
            """)
            
            result = await session.execute(query)
            return [
                {
                    "user_id": row.id,
                    "username": row.username,
                    "email": row.email,
                    "full_name": row.full_name,
                    "is_active": row.is_active,
                    "created_at": row.created_at,
                    "agent_count": row.agent_count,
                    "tool_count": row.tool_count,
                    "resource_count": row.resource_count,
                    "last_agent_update": row.last_agent_update
                }
                for row in result
            ]
    
    @staticmethod
    async def get_agent_details_report(user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Báo cáo chi tiết về agents."""
        
        async with get_db_session() as session:
            where_clause = "WHERE a.user_id = :user_id" if user_id else ""
            
            query = text(f"""
                SELECT 
                    a.id,
                    a.name,
                    a.description,
                    a.is_active,
                    a.created_at,
                    a.updated_at,
                    u.username as owner_username,
                    u.email as owner_email,
                    COUNT(DISTINCT t.id) as tool_count,
                    COUNT(DISTINCT r.id) as resource_count,
                    COUNT(DISTINCT CASE WHEN t.is_active = true THEN t.id END) as active_tool_count,
                    COUNT(DISTINCT CASE WHEN r.is_active = true THEN r.id END) as active_resource_count
                FROM agents a
                JOIN users u ON a.user_id = u.id
                LEFT JOIN tools t ON a.id = t.agent_id
                LEFT JOIN resources r ON a.id = r.agent_id
                {where_clause}
                GROUP BY a.id, a.name, a.description, a.is_active, a.created_at, a.updated_at, u.username, u.email
                ORDER BY a.created_at DESC
            """)
            
            params = {"user_id": user_id} if user_id else {}
            result = await session.execute(query, params)
            
            return [
                {
                    "agent_id": row.id,
                    "name": row.name,
                    "description": row.description,
                    "is_active": row.is_active,
                    "created_at": row.created_at,
                    "updated_at": row.updated_at,
                    "owner_username": row.owner_username,
                    "owner_email": row.owner_email,
                    "tool_count": row.tool_count,
                    "resource_count": row.resource_count,
                    "active_tool_count": row.active_tool_count,
                    "active_resource_count": row.active_resource_count
                }
                for row in result
            ]
    
    @staticmethod
    async def get_tool_usage_stats() -> List[Dict[str, Any]]:
        """Thống kê sử dụng tools theo loại."""
        
        async with get_db_session() as session:
            query = text("""
                SELECT 
                    t.tool_type,
                    COUNT(*) as total_count,
                    COUNT(CASE WHEN t.is_active = true THEN 1 END) as active_count,
                    COUNT(CASE WHEN t.is_active = false THEN 1 END) as inactive_count,
                    ROUND(AVG(CASE WHEN t.is_active = true THEN 1.0 ELSE 0.0 END) * 100, 2) as active_percentage
                FROM tools t
                GROUP BY t.tool_type
                ORDER BY total_count DESC
            """)
            
            result = await session.execute(query)
            return [
                {
                    "tool_type": row.tool_type,
                    "total_count": row.total_count,
                    "active_count": row.active_count,
                    "inactive_count": row.inactive_count,
                    "active_percentage": float(row.active_percentage)
                }
                for row in result
            ]
    
    @staticmethod
    async def get_resource_mime_type_stats() -> List[Dict[str, Any]]:
        """Thống kê resources theo MIME type."""
        
        async with get_db_session() as session:
            query = text("""
                SELECT 
                    r.mime_type,
                    COUNT(*) as count,
                    COUNT(CASE WHEN r.is_active = true THEN 1 END) as active_count,
                    AVG(LENGTH(r.content)) as avg_content_length,
                    MAX(LENGTH(r.content)) as max_content_length,
                    MIN(LENGTH(r.content)) as min_content_length
                FROM resources r
                WHERE r.content IS NOT NULL
                GROUP BY r.mime_type
                ORDER BY count DESC
            """)
            
            result = await session.execute(query)
            return [
                {
                    "mime_type": row.mime_type,
                    "count": row.count,
                    "active_count": row.active_count,
                    "avg_content_length": float(row.avg_content_length) if row.avg_content_length else 0,
                    "max_content_length": row.max_content_length or 0,
                    "min_content_length": row.min_content_length or 0
                }
                for row in result
            ]
    
    @staticmethod
    async def search_agents_by_keyword(keyword: str) -> List[Dict[str, Any]]:
        """Tìm kiếm agents theo từ khóa."""
        
        async with get_db_session() as session:
            query = text("""
                SELECT 
                    a.id,
                    a.name,
                    a.description,
                    a.is_active,
                    u.username as owner_username,
                    ts_rank(
                        to_tsvector('english', COALESCE(a.name, '') || ' ' || COALESCE(a.description, '')),
                        plainto_tsquery('english', :keyword)
                    ) as relevance_score
                FROM agents a
                JOIN users u ON a.user_id = u.id
                WHERE 
                    to_tsvector('english', COALESCE(a.name, '') || ' ' || COALESCE(a.description, ''))
                    @@ plainto_tsquery('english', :keyword)
                ORDER BY relevance_score DESC, a.name
            """)
            
            result = await session.execute(query, {"keyword": keyword})
            return [
                {
                    "agent_id": row.id,
                    "name": row.name,
                    "description": row.description,
                    "is_active": row.is_active,
                    "owner_username": row.owner_username,
                    "relevance_score": float(row.relevance_score)
                }
                for row in result
            ]
    
    @staticmethod
    async def get_recent_activity(days: int = 7) -> List[Dict[str, Any]]:
        """Lấy hoạt động gần đây trong N ngày."""
        
        async with get_db_session() as session:
            query = text("""
                SELECT 
                    'user' as entity_type,
                    u.id as entity_id,
                    u.username as entity_name,
                    u.created_at as activity_time,
                    'created' as activity_type
                FROM users u
                WHERE u.created_at >= NOW() - INTERVAL ':days days'
                
                UNION ALL
                
                SELECT 
                    'agent' as entity_type,
                    a.id as entity_id,
                    a.name as entity_name,
                    a.created_at as activity_time,
                    'created' as activity_type
                FROM agents a
                WHERE a.created_at >= NOW() - INTERVAL ':days days'
                
                UNION ALL
                
                SELECT 
                    'agent' as entity_type,
                    a.id as entity_id,
                    a.name as entity_name,
                    a.updated_at as activity_time,
                    'updated' as activity_type
                FROM agents a
                WHERE a.updated_at >= NOW() - INTERVAL ':days days'
                AND a.updated_at != a.created_at
                
                ORDER BY activity_time DESC
                LIMIT 50
            """)
            
            result = await session.execute(query, {"days": days})
            return [
                {
                    "entity_type": row.entity_type,
                    "entity_id": row.entity_id,
                    "entity_name": row.entity_name,
                    "activity_time": row.activity_time,
                    "activity_type": row.activity_type
                }
                for row in result
            ]
    
    @staticmethod
    async def cleanup_inactive_data(days: int = 30) -> Dict[str, int]:
        """Dọn dẹp dữ liệu không hoạt động."""
        
        async with get_db_session() as session:
            # Xóa tools không hoạt động
            tools_query = text("""
                DELETE FROM tools 
                WHERE is_active = false 
                AND updated_at < NOW() - INTERVAL ':days days'
            """)
            
            tools_result = await session.execute(tools_query, {"days": days})
            
            # Xóa resources không hoạt động
            resources_query = text("""
                DELETE FROM resources 
                WHERE is_active = false 
                AND updated_at < NOW() - INTERVAL ':days days'
            """)
            
            resources_result = await session.execute(resources_query, {"days": days})
            
            return {
                "deleted_tools": tools_result.rowcount,
                "deleted_resources": resources_result.rowcount
            }
