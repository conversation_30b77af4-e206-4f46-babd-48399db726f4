"""
GHTK Resources cho Shipment MCP Server

Module này chứa các resources để lấy dữ liệu từ GHTK API
"""

import json
from mcp.server.fastmcp import FastMCP

# Import với fallback
try:
    from src.server.redai_system.shipment.utils.ghtk_client import (
        ghtk_get_solutions,
        ghtk_get_order_status,
        ghtk_get_pick_addresses,
        ghtk_search_products
    )
except ImportError:
    from utils.ghtk_client import (
        ghtk_get_solutions,
        ghtk_get_order_status,
        ghtk_get_pick_addresses,
        ghtk_search_products
    )

def register_ghtk_resources(mcp: FastMCP):
    """Đăng ký tất cả GHTK resources với MCP server"""

    @mcp.resource("ghtk://solutions")
    async def get_ghtk_solutions() -> str:
        """
        Lấy danh sách giải pháp (gói bảo hiểm/phụ phí) từ GHTK
        
        Resource này trả về danh sách các giải pháp mà shop có thể áp dụng
        khi tạo đơn hàng, bao gồm các gói bảo hiểm và phụ phí.
        
        Returns:
            JSON string chứa danh sách giải pháp
        """
        try:
            result = await ghtk_get_solutions()
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách giải pháp GHTK: {str(e)}"

    @mcp.resource("ghtk://order/{tracking_order}")
    async def get_ghtk_order_status(tracking_order: str) -> str:
        """
        Lấy thông tin trạng thái đơn hàng GHTK
        
        Args:
            tracking_order: Mã vận đơn GHTK hoặc mã đơn đối tác
        
        Returns:
            JSON string chứa thông tin chi tiết đơn hàng và trạng thái hiện tại
        """
        try:
            result = await ghtk_get_order_status(tracking_order)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy thông tin đơn hàng GHTK {tracking_order}: {str(e)}"

    @mcp.resource("ghtk://pick-addresses")
    async def get_ghtk_pick_addresses() -> str:
        """
        Lấy danh sách địa chỉ lấy hàng (kho) đã cấu hình trên GHTK
        
        Resource này trả về danh sách các địa chỉ kho hàng mà shop đã
        cài đặt trên hệ thống quản lý GHTK. Có thể sử dụng pick_address_id
        từ danh sách này khi tạo đơn hàng thay vì nhập địa chỉ thủ công.
        
        Returns:
            JSON string chứa danh sách địa chỉ lấy hàng
        """
        try:
            result = await ghtk_get_pick_addresses()
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách địa chỉ lấy hàng GHTK: {str(e)}"

    @mcp.resource("ghtk://products")
    async def get_ghtk_products() -> str:
        """
        Lấy danh sách tất cả sản phẩm đã tạo trên hệ thống GHTK
        
        Resource này trả về danh sách sản phẩm mà shop đã tạo/đăng lên GHTK.
        Để tìm kiếm sản phẩm cụ thể, sử dụng resource ghtk://products/search/{term}
        
        Returns:
            JSON string chứa danh sách sản phẩm
        """
        try:
            # Tìm kiếm với term rỗng để lấy tất cả sản phẩm
            result = await ghtk_search_products("")
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách sản phẩm GHTK: {str(e)}"

    @mcp.resource("ghtk://products/search/{term}")
    async def search_ghtk_products(term: str) -> str:
        """
        Tìm kiếm sản phẩm theo từ khóa trên hệ thống GHTK
        
        Args:
            term: Từ khóa tên sản phẩm cần tìm
        
        Returns:
            JSON string chứa danh sách sản phẩm tìm được
        """
        try:
            result = await ghtk_search_products(term)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi tìm kiếm sản phẩm GHTK với từ khóa '{term}': {str(e)}"

    @mcp.resource("ghtk://order-history")
    async def get_ghtk_order_history() -> str:
        """
        Lấy lịch sử đơn hàng GHTK (placeholder)
        
        Hiện tại GHTK API không cung cấp endpoint để lấy danh sách đơn hàng.
        Resource này được tạo để tương lai mở rộng.
        
        Returns:
            JSON string thông báo chức năng chưa có
        """
        return json.dumps({
            "success": False,
            "message": "GHTK API hiện tại không hỗ trợ lấy danh sách đơn hàng. Chỉ có thể tra cứu từng đơn bằng mã vận đơn.",
            "suggestion": "Sử dụng resource ghtk://order/{tracking_order} để tra cứu đơn hàng cụ thể"
        }, ensure_ascii=False, indent=2)

    @mcp.resource("ghtk://webhook-info")
    async def get_ghtk_webhook_info() -> str:
        """
        Thông tin về webhook GHTK
        
        Resource này cung cấp thông tin về cách GHTK gửi webhook
        để thông báo cập nhật trạng thái đơn hàng.
        
        Returns:
            JSON string chứa thông tin webhook
        """
        webhook_info = {
            "webhook_description": "GHTK gửi webhook để thông báo cập nhật trạng thái đơn hàng",
            "method": "POST",
            "content_type": "application/x-www-form-urlencoded hoặc application/json",
            "parameters": {
                "label_id": "Mã vận đơn GHTK",
                "partner_id": "Mã đơn bên đối tác",
                "status_id": "Mã trạng thái mới của đơn",
                "action_time": "Thời gian cập nhật trạng thái (ISO 8601)",
                "reason_code": "Mã lý do (nếu có)",
                "reason": "Mô tả chi tiết lý do",
                "weight": "Trọng lượng đơn hàng (kg)",
                "fee": "Phí ship đã áp dụng (VNĐ)",
                "return_part_package": "1 nếu giao hàng một phần, 0 nếu không"
            },
            "response_requirement": "Đối tác phải trả về HTTP 200 để xác nhận nhận webhook thành công",
            "retry_policy": "GHTK sẽ gửi lại vài lần nếu không nhận được HTTP 200",
            "example_payload": {
                "partner_id": "1234567",
                "label_id": "S1.A1.17373471",
                "status_id": 5,
                "action_time": "2016-11-02T12:18:39+07:00",
                "reason_code": "",
                "reason": "",
                "weight": 2.4,
                "fee": 15000,
                "pick_money": 100000,
                "return_part_package": 0
            }
        }
        
        return json.dumps(webhook_info, ensure_ascii=False, indent=2)

    @mcp.resource("ghtk://status-codes")
    async def get_ghtk_status_codes() -> str:
        """
        Danh sách mã trạng thái đơn hàng GHTK
        
        Resource này cung cấp thông tin về các mã trạng thái
        mà GHTK sử dụng để theo dõi đơn hàng.
        
        Returns:
            JSON string chứa danh sách mã trạng thái
        """
        status_codes = {
            "status_codes": {
                "1": "Chưa tiếp nhận",
                "2": "Đã tiếp nhận",
                "3": "Đã lấy hàng/Đã nhập kho",
                "4": "Đã điều phối giao hàng/Đang giao hàng",
                "5": "Đã giao hàng/Chưa đối soát",
                "6": "Đã đối soát",
                "7": "Không lấy được hàng",
                "8": "Hoãn lấy hàng",
                "9": "Không giao được hàng",
                "10": "Delay giao hàng",
                "11": "Đã đối soát công nợ trả hàng",
                "12": "Đang lấy hàng",
                "13": "Đơn hàng bồi hoàn",
                "20": "Đang trả hàng (COD cầm hàng)",
                "21": "Đã trả hàng (COD đã trả hàng)",
                "123": "Shipper báo đã lấy hàng",
                "127": "Shipper (nhân viên lấy/giao hàng) báo không lấy được hàng",
                "128": "Shipper báo delay lấy hàng",
                "410": "Shipper báo đã giao hàng",
                "412": "Shipper báo không giao được giao hàng",
                "413": "Shipper báo delay giao hàng"
            },
            "note": "Các mã trạng thái này được sử dụng trong API lấy trạng thái đơn hàng và webhook"
        }
        
        return json.dumps(status_codes, ensure_ascii=False, indent=2)
