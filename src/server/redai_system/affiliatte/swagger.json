{"openapi": "3.0.0", "info": {"title": "Affiliate Module API", "description": "API documentation for Affiliate Module - Quản lý hệ thống affiliate cho người dùng bao gồm tà<PERSON>, th<PERSON><PERSON> k<PERSON>, r<PERSON><PERSON> ti<PERSON>, thông tin doanh nghiệp và đăng ký", "version": "1.0.0", "contact": {"name": "RedAI Development Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.redai.com", "description": "Production server"}], "tags": [{"name": "User - Affiliate Statistics", "description": "Thống kê affiliate cho người dùng"}, {"name": "User - Affiliate Account", "description": "Quản lý tài khoản affiliate cho người dùng"}, {"name": "User - Affiliate Order", "description": "Q<PERSON>ản lý đơn hàng affiliate cho người dùng"}, {"name": "User - Affiliate <PERSON>", "description": "Q<PERSON>ản lý rút tiền affiliate cho người dùng"}, {"name": "User - Affiliate Customer", "description": "<PERSON><PERSON>ản lý khách hàng affiliate cho người dùng"}, {"name": "User - Affiliate Point Conversion", "description": "<PERSON><PERSON><PERSON><PERSON> lý chuyển đổi điểm affiliate cho người dùng"}, {"name": "User - Affiliate Referral Link", "description": "<PERSON><PERSON><PERSON><PERSON> lý liên kết giới thiệu affiliate cho người dùng"}, {"name": "User - Affiliate Upload", "description": "Upload tài liệu affiliate cho người dùng"}, {"name": "User - Affiliate Business", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin doanh nghiệp affiliate cho người dùng"}, {"name": "User - Affiliate Registration", "description": "Đăng ký affiliate cho người dùng"}], "paths": {"/user/affiliate/statistics": {"get": {"tags": ["User - Affiliate Statistics"], "summary": "<PERSON><PERSON><PERSON> thông tin thống kê tài k<PERSON>n affiliate", "description": "<PERSON><PERSON><PERSON> thông tin thống kê tổng quan về tài khoản affiliate của người dùng hiện tại", "security": [{"bearerAuth": []}], "parameters": [{"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON><PERSON> l<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kết thúc lọ<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-12-31"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin thống kê thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin thống kê thành công"}, "data": {"$ref": "#/components/schemas/AffiliateStatisticsDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/account": {"get": {"tags": ["User - Affiliate Account"], "summary": "<PERSON><PERSON><PERSON> thông tin tài khoản affiliate của người dùng", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết tài khoản affiliate của người dùng hiện tại", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin tài khoản affiliate thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin tài khoản affiliate thành công"}, "data": {"$ref": "#/components/schemas/UserAffiliateAccountDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/withdrawals": {"get": {"tags": ["User - Affiliate <PERSON>"], "summary": "<PERSON><PERSON><PERSON> danh sách yêu cầu rút tiền", "description": "<PERSON><PERSON><PERSON> danh sách yêu cầu rút tiền của người dùng hiện tại với phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái yêu cầu rút tiền", "required": false, "schema": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "COMPLETED"], "example": "PENDING"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách yêu cầu rút tiền thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách yêu cầu rút tiền thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AffiliateWithdrawalDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Affiliate <PERSON>"], "summary": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u rút tiền", "description": "<PERSON><PERSON><PERSON> y<PERSON>u cầu rút tiền mới cho tài k<PERSON>n affiliate", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWithdrawRequestDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> y<PERSON>u cầu rút tiền thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> y<PERSON>u cầu rút tiền thành công"}, "data": {"$ref": "#/components/schemas/WithdrawResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/referral-links": {"get": {"tags": ["User - Affiliate Referral Link"], "summary": "<PERSON><PERSON><PERSON> danh sách liên kết giới thiệu", "description": "<PERSON><PERSON><PERSON> danh sách liên kết giới thiệu của người dùng hiện tại", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách liên kết giới thiệu thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách liên kết giới thiệu thành công"}, "data": {"$ref": "#/components/schemas/AffiliateReferralLinkDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/business": {"get": {"tags": ["User - Affiliate Business"], "summary": "<PERSON><PERSON><PERSON> thông tin doanh nghi<PERSON>p", "description": "<PERSON><PERSON><PERSON> thông tin doanh nghiệp của tài k<PERSON> affiliate", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin doanh nghiệp thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin doanh nghiệp thành công"}, "data": {"$ref": "#/components/schemas/AffiliateBusinessDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Affiliate Business"], "summary": "<PERSON><PERSON><PERSON> thông tin doanh nghi<PERSON>p", "description": "T<PERSON><PERSON> thông tin doanh nghiệp cho tài k<PERSON>ản affiliate", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAffiliateBusinessDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> thông tin doanh nghiệp thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin doanh nghiệp thành công"}, "data": {"$ref": "#/components/schemas/AffiliateBusinessDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "patch": {"tags": ["User - Affiliate Business"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin doanh nghiệp", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin doanh nghiệp của tài <PERSON> affiliate", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAffiliateBusinessDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật thông tin doanh nghiệp thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhật thông tin doanh nghiệp thành công"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"AffiliateStatisticsDto": {"type": "object", "properties": {"totalEarned": {"type": "number", "format": "float", "description": "Tổng số tiền đã kiếm đư<PERSON>", "example": 5000.0}, "availableBalance": {"type": "number", "format": "float", "description": "Số dư hiện tại có thể rút", "example": 2000.0}, "totalClicks": {"type": "integer", "description": "<PERSON><PERSON>ng số l<PERSON> click", "example": 150}, "totalOrders": {"type": "integer", "description": "Tổng số đơn hàng", "example": 25}, "totalCustomers": {"type": "integer", "description": "<PERSON><PERSON>ng số khách hàng giới thiệu", "example": 20}, "conversionRate": {"type": "number", "format": "float", "description": "Tỷ lệ chuyển đổi (%)", "example": 16.67}, "thisMonthEarned": {"type": "number", "format": "float", "description": "<PERSON><PERSON> tiền kiếm đư<PERSON><PERSON> trong tháng này", "example": 500.0}}, "required": ["totalEarned", "availableBalance", "totalClicks", "totalOrders", "totalCustomers", "conversionRate", "thisMonthEarned"]}, "UserAffiliateAccountDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của tài khoản affiliate", "example": 1}, "status": {"type": "string", "enum": ["DRAFT", "PENDING_APPROVAL", "APPROVED", "REJECTED", "ACTIVE", "INACTIVE"], "description": "Trạng thái tài k<PERSON>n affiliate", "example": "ACTIVE"}, "totalEarned": {"type": "number", "format": "float", "description": "Tổng số tiền đã kiếm đư<PERSON>", "example": 5000.0}, "availableBalance": {"type": "number", "format": "float", "description": "Số dư hiện tại còn lại", "example": 2000.0}, "accountType": {"type": "string", "enum": ["PERSONAL", "BUSINESS"], "description": "<PERSON><PERSON><PERSON> tà<PERSON>", "example": "PERSONAL"}, "referralCode": {"type": "string", "description": "<PERSON>ã giới thiệu", "example": "REF123456"}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "example": "2024-01-15T10:30:00Z"}}, "required": ["id", "status", "totalEarned", "availableBalance", "accountType", "createdAt"]}, "AffiliateWithdrawalDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "<PERSON> của yêu cầu rút tiền", "example": 1}, "amount": {"type": "number", "format": "float", "description": "<PERSON><PERSON> tiền yêu cầu r<PERSON>t", "example": 1000.0}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "COMPLETED"], "description": "<PERSON>r<PERSON><PERSON> thái yêu cầu rút tiền", "example": "PENDING"}, "bankAccount": {"type": "string", "description": "Số tài k<PERSON>n ngân hàng", "example": "**********"}, "bankName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "example": "Vietcombank"}, "accountHolderName": {"type": "string", "description": "<PERSON>ên chủ tài k<PERSON>n", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "requestedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian yêu c<PERSON>u", "example": "2024-01-15T10:30:00Z"}, "processedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian xử lý", "example": "2024-01-16T14:20:00Z", "nullable": true}}, "required": ["id", "amount", "status", "bankAccount", "bankName", "accountHolderName", "requestedAt"]}, "CreateWithdrawRequestDto": {"type": "object", "properties": {"amount": {"type": "number", "format": "float", "minimum": 100000, "description": "<PERSON><PERSON> tiền yêu cầu rú<PERSON> (tối thiểu 100,000 VND)", "example": 1000000.0}, "bankAccount": {"type": "string", "description": "Số tài k<PERSON>n ngân hàng", "example": "**********"}, "bankName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "example": "Vietcombank"}, "accountHolderName": {"type": "string", "description": "<PERSON>ên chủ tài k<PERSON>n", "example": "<PERSON><PERSON><PERSON><PERSON>"}}, "required": ["amount", "bankAccount", "bankName", "accountHolderName"]}, "WithdrawResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "<PERSON> của yêu cầu rút tiền", "example": 1}, "amount": {"type": "number", "format": "float", "description": "<PERSON><PERSON> tiền yêu cầu r<PERSON>t", "example": 1000000.0}, "status": {"type": "string", "enum": ["PENDING"], "description": "<PERSON>r<PERSON><PERSON> thái yêu cầu rút tiền", "example": "PENDING"}, "requestedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian yêu c<PERSON>u", "example": "2024-01-15T10:30:00Z"}}, "required": ["id", "amount", "status", "requestedAt"]}, "AffiliateReferralLinkDto": {"type": "object", "properties": {"referralCode": {"type": "string", "description": "<PERSON>ã giới thiệu", "example": "REF123456"}, "links": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["REGISTRATION", "PRODUCT", "SUBSCRIPTION"], "description": "<PERSON><PERSON><PERSON> liên k<PERSON>t", "example": "REGISTRATION"}, "url": {"type": "string", "description": "URL liên kết", "example": "https://redai.com/register?ref=REF123456"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả liên kết", "example": "<PERSON><PERSON><PERSON> kết đăng ký tài k<PERSON>n"}}, "required": ["type", "url", "description"]}}, "statistics": {"type": "object", "properties": {"totalClicks": {"type": "integer", "description": "<PERSON><PERSON>ng số l<PERSON> click", "example": 150}, "totalConversions": {"type": "integer", "description": "Tổng số chuyển đổi", "example": 25}, "conversionRate": {"type": "number", "format": "float", "description": "Tỷ lệ chuyển đổi (%)", "example": 16.67}}, "required": ["totalClicks", "totalConversions", "conversionRate"]}}, "required": ["referralCode", "links", "statistics"]}, "AffiliateBusinessDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID thông tin doanh nghiệp", "example": 1}, "companyName": {"type": "string", "description": "<PERSON><PERSON>n công ty", "example": "Công ty TNHH ABC"}, "taxCode": {"type": "string", "description": "<PERSON><PERSON> số thuế", "example": "**********"}, "address": {"type": "string", "description": "Địa chỉ công ty", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "**********"}, "email": {"type": "string", "description": "<PERSON>ail công ty", "example": "<EMAIL>"}, "website": {"type": "string", "description": "Website công ty", "example": "https://abc.com", "nullable": true}, "businessLicenseUrl": {"type": "string", "description": "URL giấy ph<PERSON>p kinh doanh", "example": "https://s3.amazonaws.com/bucket/business-license.pdf"}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED"], "description": "<PERSON>r<PERSON><PERSON> thái phê <PERSON>", "example": "APPROVED"}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "example": "2024-01-15T10:30:00Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t", "example": "2024-01-20T15:45:00Z"}}, "required": ["id", "companyName", "taxCode", "address", "phoneNumber", "email", "businessLicenseUrl", "status", "createdAt", "updatedAt"]}, "CreateAffiliateBusinessDto": {"type": "object", "properties": {"companyName": {"type": "string", "description": "<PERSON><PERSON>n công ty", "example": "Công ty TNHH ABC"}, "taxCode": {"type": "string", "description": "<PERSON><PERSON> số thuế", "example": "**********"}, "address": {"type": "string", "description": "Địa chỉ công ty", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "**********"}, "email": {"type": "string", "description": "<PERSON>ail công ty", "example": "<EMAIL>"}, "website": {"type": "string", "description": "Website công ty", "example": "https://abc.com", "nullable": true}, "businessLicenseUrl": {"type": "string", "description": "URL giấy ph<PERSON>p kinh doanh", "example": "https://s3.amazonaws.com/bucket/business-license.pdf"}}, "required": ["companyName", "taxCode", "address", "phoneNumber", "email", "businessLicenseUrl"]}, "UpdateAffiliateBusinessDto": {"type": "object", "properties": {"companyName": {"type": "string", "description": "<PERSON><PERSON>n công ty", "example": "Công ty TNHH ABC Updated"}, "taxCode": {"type": "string", "description": "<PERSON><PERSON> số thuế", "example": "**********"}, "address": {"type": "string", "description": "Địa chỉ công ty", "example": "456 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 2, TP.<PERSON>M"}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "**********"}, "email": {"type": "string", "description": "<PERSON>ail công ty", "example": "<EMAIL>"}, "website": {"type": "string", "description": "Website công ty", "example": "https://abc-updated.com", "nullable": true}, "businessLicenseUrl": {"type": "string", "description": "URL giấy ph<PERSON>p kinh doanh", "example": "https://s3.amazonaws.com/bucket/business-license-updated.pdf"}}}, "PaginationMeta": {"type": "object", "properties": {"page": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "limit": {"type": "integer", "description": "Số lượng item mỗi trang", "example": 10}, "total": {"type": "integer", "description": "Tổng số item", "example": 100}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 10}}, "required": ["page", "limit", "total", "totalPages"]}}, "responses": {"BadRequest": {"description": "<PERSON><PERSON><PERSON> c<PERSON>u kh<PERSON>ng h<PERSON>p lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation failed"}, "errorCode": {"type": "integer", "example": 400}}}}}}, "Unauthorized": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}, "errorCode": {"type": "integer", "example": 401}}}}}}, "Forbidden": {"description": "<PERSON><PERSON> cấm truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Forbidden"}, "errorCode": {"type": "integer", "example": 403}}}}}}, "NotFound": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Resource not found"}, "errorCode": {"type": "integer", "example": 404}}}}}}, "InternalServerError": {"description": "Lỗi máy chủ nội bộ", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}, "errorCode": {"type": "integer", "example": 500}}}}}}}}}