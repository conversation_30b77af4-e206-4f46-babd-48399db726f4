# RedAI User API MCP Server

Server MCP cho RedAI User API sử dụng FastMCP framework và OpenAPI schema tự động.

## Tổng quan

Server này tự động tạo MCP tools và resources từ file `swagger-user-schema.json`, cung cấp giao diện MCP cho tất cả các endpoint của RedAI User API.

### Tính năng chính

- ✅ **Tự động tạo từ OpenAPI**: Sử dụng `FastMCP.from_openapi()` để tự động tạo tools/resources
- ✅ **JWT Authentication**: Hỗ trợ xác thực JWT cho tất cả API calls
- ✅ **HTTP Transport**: Chạy với Streamable HTTP transport
- ✅ **Route Mapping**: Tùy chỉnh cách map endpoints thành MCP components
- ✅ **Type Safety**: Tự động validation parameters và response schemas

### API Coverage

Server hỗ trợ tất cả endpoints trong swagger-user-schema.json:

#### 📁 Media Management
- `GET /v1/media/my-media` → Resource: `get_my_media`
- `DELETE /v1/media/my-media` → Tool: `delete_my_media`
- `GET /v1/media/{id}` → ResourceTemplate: `get_media_by_id`
- `POST /v1/media/presigned-urls` → Tool: `create_media_presigned_urls`

#### 👤 Account Management
- `GET /v1/user/account/bank-info` → Resource: `get_bank_info`
- `PUT /v1/user/account/bank-info` → Tool: `update_bank_info`
- `GET /v1/user/account/business-info` → Resource: `get_business_info`
- `PUT /v1/user/account/business-info` → Tool: `update_business_info`
- `POST /v1/user/account/avatar/upload-url` → Tool: `create_avatar_upload_url`
- `PUT /v1/user/account/avatar` → Tool: `update_avatar`
- `GET /v1/user/account/notification-settings` → Resource: `get_notification_settings`
- `PUT /v1/user/account/notification-settings` → Tool: `update_notification_settings`

#### 📚 Knowledge Files
- `POST /v1/user/knowledge-files/batch` → Tool: `batch_create_knowledge_files`

## Cài đặt và Cấu hình

### 1. Cài đặt Dependencies

```bash
# Cài đặt project dependencies
pip install -e .

# Hoặc cài đặt trực tiếp
pip install fastmcp httpx python-dotenv
```

### 2. Cấu hình Environment

```bash
# Sao chép file cấu hình mẫu
cp .env.example .env

# Chỉnh sửa .env với thông tin thực tế
nano .env
```

Các biến môi trường quan trọng:

```bash
# RedAI User API Configuration
REDAI_USER_API_BASE_URL=https://api.redai.com
REDAI_JWT_TOKEN=your_jwt_token_here

# HTTP Server Configuration
USER_API_HTTP_HOST=127.0.0.1
USER_API_HTTP_PORT=8001
USER_API_HTTP_PATH=/mcp
```

### 3. Test Server

```bash
# Chạy test để kiểm tra cấu hình
python test_user_api_server.py
```

### 4. Khởi động Server

```bash
# Chạy server
python src/server/redai_system/user_api_server.py
```

Server sẽ khởi động tại:
- **MCP Endpoint**: `http://127.0.0.1:8001/mcp`
- **Server URL**: `http://127.0.0.1:8001`

## Sử dụng

### Kết nối từ MCP Client

```python
from fastmcp import Client

async def main():
    # Kết nối qua HTTP
    async with Client("http://127.0.0.1:8001/mcp") as client:
        # List available tools
        tools = await client.list_tools()
        print(f"Available tools: {len(tools.tools)}")
        
        # List available resources
        resources = await client.list_resources()
        print(f"Available resources: {len(resources.resources)}")
        
        # Call a tool
        result = await client.call_tool("get_my_media", {
            "page": 1,
            "limit": 10
        })
        print(result.text)
```

### Cấu hình Claude Desktop

Thêm vào `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "redai-user-api": {
      "url": "http://127.0.0.1:8001/mcp"
    }
  }
}
```

## Kiến trúc

### Route Mapping Strategy

Server sử dụng custom route mapping để tối ưu trải nghiệm MCP:

```python
# GET endpoints với path parameters → ResourceTemplates
RouteMap(methods=["GET"], pattern=r".*\{.*\}.*", mcp_type=MCPType.RESOURCE_TEMPLATE)

# GET endpoints khác → Resources  
RouteMap(methods=["GET"], pattern=r".*", mcp_type=MCPType.RESOURCE)

# POST, PUT, DELETE → Tools
RouteMap(methods=["POST", "PUT", "DELETE"], pattern=r".*", mcp_type=MCPType.TOOL)
```

### Authentication Flow

1. JWT token được cấu hình trong environment variables
2. HTTP client tự động thêm `Authorization: Bearer <token>` header
3. Tất cả API calls đều được authenticate tự động

### Component Customization

- **Tools**: Prefix `🔧` + tag `api-action`
- **Resources**: Prefix `📊` + tag `api-data`  
- **ResourceTemplates**: Prefix `📋` + tag `api-detail`
- **Path-based tags**: `media`, `account`, `knowledge`
- **Security tags**: `authenticated` cho endpoints yêu cầu JWT

## Troubleshooting

### Lỗi thường gặp

1. **Schema not found**
   ```
   FileNotFoundError: Không tìm thấy file schema tại: swagger-user-schema.json
   ```
   - Đảm bảo file `swagger-user-schema.json` tồn tại trong thư mục server

2. **JWT Token missing**
   ```
   ⚠️ CẢNH BÁO: JWT Token chưa được cấu hình!
   ```
   - Thiết lập `REDAI_JWT_TOKEN` trong file `.env`

3. **Import errors**
   ```
   ImportError: No module named 'fastmcp'
   ```
   - Chạy `pip install -e .` để cài đặt dependencies

### Debug Mode

Để bật debug mode, thêm vào `.env`:

```bash
DEBUG=true
LOG_LEVEL=DEBUG
```

## Phát triển

### Thêm Custom Logic

Để thêm logic tùy chỉnh, chỉnh sửa các hàm trong `user_api_server.py`:

- `customize_mcp_components()`: Tùy chỉnh components sau khi tạo
- `create_route_maps()`: Thay đổi route mapping strategy
- `customize_component_names()`: Đổi tên components

### Testing

```bash
# Chạy test cơ bản
python test_user_api_server.py

# Test với JWT token
REDAI_JWT_TOKEN=your_token python test_user_api_server.py
```

## Tích hợp

Server này có thể được tích hợp với:

- **Claude Desktop**: Qua HTTP transport
- **Custom MCP Clients**: Qua FastMCP Client
- **Other MCP Servers**: Qua server composition
- **Web Applications**: Qua HTTP API calls

## Tài liệu tham khảo

- [FastMCP Documentation](https://gofastmcp.com)
- [Model Context Protocol](https://modelcontextprotocol.io)
- [OpenAPI Integration](https://gofastmcp.com/servers/openapi)
