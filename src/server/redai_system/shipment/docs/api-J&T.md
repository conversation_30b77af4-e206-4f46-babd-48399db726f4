# Tài liệu API J&T Express

## Đ<PERSON>n hàng (Order)
### API Đặt đơn hàng cơ bản (Basic Order)
**Mô tả:** API này dùng để đẩy một đơn hàng từ hệ thống thương mại điện tử (marketplace/e-commerce) đến J&T, sau đó J&T sẽ tạo mã vận đơn (AWB) phản hồi lại cho đối tác ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=API%20BASIC%20ORDER)).  

**Phương thức:** `POST` (Content-Type: `application/x-www-form-urlencoded`) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=API%20Details)).  

**Tham số đầu vào:**  

- **<PERSON>ê<PERSON> ngoài (POST form):**  
  | Tên tham số  | Kiểu dữ liệu | <PERSON><PERSON><PERSON> buộc (Y/N) | <PERSON><PERSON> tả                                                            |
  |-------------|-------------|---------------|------------------------------------------------------------------|
  | `data_param`| String      | Y             | Dữ liệu yêu cầu chính: một chuỗi JSON chứa mảng các đơn hàng chi tiết ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=data_param)). |
  | `data_sign` | String      | Y             | Chữ ký bảo mật: chuỗi mã hóa (MD5 + Base64) từ `data_param` + *key* (API Key riêng) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=data_sign)). |

- **Bên trong `data_param` (chi tiết đơn hàng):**  
  Dữ liệu `data_param` chứa một mảng JSON, mỗi phần tử là một đối tượng đơn hàng với các trường sau:  
  | Tên tham số       | Kiểu dữ liệu  | Bắt buộc (Y/N) | Mô tả                                                                          |
  |-------------------|--------------|---------------|--------------------------------------------------------------------------------|
  | `username`        | String       | Y             | Tên đăng nhập tài khoản đối tác (do J&T cấp trên Dashboard) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=username)).     |
  | `api_key`         | String       | Y             | Khoá API được cấp (kiểm tra trên Dashboard) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=api_key)).                     |
  | `orderid`         | String (20)  | Y             | Mã đơn hàng từ hệ thống của đối tác (ví dụ: đơn từ e-commerce) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=orderid)). Nên thêm tiền tố riêng để tránh trùng lặp ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=note%20%3A)).  |
  | `shipper_name`    | String (30)  | Y             | Tên người gửi (sender) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=shipper_name)).                                         |
  | `shipper_contact` | String (30)  | Y             | Tên liên hệ của người gửi (thường giống `shipper_name`) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=shipper_contact)).        |
  | `shipper_phone`   | String (15)  | Y             | Số điện thoại người gửi (định dạng +62xxxxxxxxxxx) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=shipper_phone)).            |
  | `shipper_addr`    | String (200) | Y             | Địa chỉ người gửi ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=shipper_addr)).                                               |
  | `origin_code`     | String (3)   | Y             | Mã thành phố/nơi gửi (3 ký tự, viết hoa, ví dụ `JKT`) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=origin_code)).           |
  | `receiver_name`   | String (30)  | Y             | Tên người nhận ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=receiver_name)).                                                 |
  | `receiver_phone`  | String (15)  | Y             | Số điện thoại người nhận (định dạng +62xxxxxxxxxxx) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=receiver_phone)).            |
  | `receiver_addr`   | String (200) | Y             | Địa chỉ người nhận ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=receiver_addr)).                                             |
  | `receiver_zip`    | String (5)   | Y             | Mã bưu điện người nhận (nếu trống có thể điền `"00000"`) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=receiver_zip)).       |
  | `destination_code`| String (3)   | Y             | Mã thành phố/đích (3 ký tự, viết hoa, ví dụ `JKT`) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=destination_code)).              |
  | `receiver_area`   | String (10)  | Y             | Mã quận/huyện đích (ví dụ `JKT001`, viết hoa) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=receiver_area)).                  |
  | `qty`            | int         | Y             | Số lượng mặt hàng trong gói ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=qty)).                                     |
  | `weight`         | double       | Y             | Trọng lượng gói (kg, có thể có 2 chữ số thập phân) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=weight)).             |
  | `goodsdesc`      | String (40)  | Y             | Mô tả hàng hoá (không dùng ký tự đặc biệt, tối đa 40 ký tự) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=weight)).     |
  | `servicetype`    | int          | Y             | Loại dịch vụ: `1` = Pickup (đi lấy hàng), `6` = Drop-off (gửi tại điểm) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=1%20%3D%20Pickup%20service)).  |
  | `insurance`      | int          | N             | Giá trị bảo hiểm (nếu có) tính theo thỏa thuận, có thể để trống nếu không có bảo hiểm ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=insurance)). |
  | `orderdate`      | String       | Y             | Thời gian đặt đơn (định dạng `YYYY-MM-DD hh:mm:ss`, giờ địa phương UTC+7) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=orderdate)). |
  | `item_name`      | String (50)  | Y             | Tên mặt hàng (ví dụ: “Phone”) trong gói ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=item_name)).                       |
  | `cod`            | int          | N             | Giá trị thu hộ (COD) (lên đến 8 chữ số) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=cod)).                        |
  | `sendstarttime`  | String       | Y             | Thời gian bắt đầu khung giờ lấy hàng (định dạng `YYYY-MM-DD hh:mm:ss`, UTC+7) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=sendendtime)). |
  | `sendendtime`    | String       | Y             | Thời gian kết thúc khung giờ lấy hàng (định dạng `YYYY-MM-DD hh:mm:ss`, UTC+7) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=sendendtime)). |
  | `expresstype`    | String       | Y             | Loại hình vận chuyển: luôn điền `"1"` (tương ứng với “EZ” – giao hàng thường) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=expresstype)). |
  | `goodsvalue`     | int          | Y             | Giá trị hàng hóa (nếu có, tối đa 8 chữ số) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=goodsvalue)).                     |

**Đối tượng sử dụng (phản hồi):** API trả về cấu trúc JSON bao gồm khóa `success`, `desc` và `detail`. Khóa `detail` là mảng các đối tượng kết quả đặt đơn. Mỗi đối tượng trong mảng có các trường:  
- `awb_no` (String): Mã vận đơn (AWB) do J&T cấp.  
- `orderid` (String): Mã đơn hàng (như gửi ở yêu cầu).  
- `desCode` (String): Mã đích (thành phố-quận) của đơn hàng.  
- `etd` (String): Thời gian giao dự kiến (trong ví dụ là khoảng ngày, ví dụ `"2-4"`).  
- `status` (String): Trạng thái xử lý (thường là `"Sukses"` nếu thành công) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=%7B%20%22awb_no%22%3A%20%22JO0027364832%22%2C%20%22orderid%22%3A%20%22ORDERID,4%22%2C%20%22status%22%3A%20%22Sukses%22%2C%20%7D)).  

Ví dụ phản hồi thành công: 
```json
{
  "success": true,
  "desc": "Request berhasil",
  "detail": [
    {
      "awb_no": "JO0027364832",
      "orderid": "ORDERID-0001",
      "desCode": "JKT-JKT001",
      "etd": "2-4",
      "status": "Sukses"
    }
  ]
}
```  

## Tra cứu vận đơn (Tracking)
### API Tra cứu trạng thái đơn hàng (Tracking API)
**Mô tả:** API này dùng để tra cứu tình trạng và lịch sử vận chuyển của một lô hàng theo mã vận đơn ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=TRACKING%20API)). 

**Phương thức:** `POST` (Authorization: Basic Auth) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=API%20Details)).  

**Tham số đầu vào:**  
| Tên tham số  | Kiểu dữ liệu | Bắt buộc (Y/N) | Mô tả                           |
|-------------|-------------|---------------|---------------------------------|
| `awb`       | String      | Y             | Mã vận đơn (AWB) cần tra cứu.   |
| `eccompanyid`| String     | Y             | Mã cửa hàng/đối tác (client ID) được cung cấp trên Dashboard. |

**Đối tượng sử dụng (phản hồi):** Phản hồi JSON gồm các trường `error_id`, `error_message`, `awb`, `orderid` và `detail`. Trường `detail` là một đối tượng chứa thông tin chi tiết đơn hàng gồm:  
- `shipped_date` (String): Thời gian J&T xử lý lô hàng (dạng timestamp) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=)).  
- `services_code` (String): Mã loại dịch vụ.  
- `actual_amount` (int): Tổng cước phí đơn hàng.  
- `weight` (int): Trọng lượng gói (kg).  
- `qty` (int): Số lượng sản phẩm.  
- `itemname` (String): Tên sản phẩm chính trong gói.  
- `detail_cost` (Object): **Đối tượng chi tiết chi phí** gồm các trường con:  
  - `shipping_cost` (int): Phí giao hàng chính ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=)).  
  - `add_cost` (int): Phí phụ thu (nếu có, ví dụ đóng gỗ) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=)).  
  - `insurance_cost` (int): Phí bảo hiểm (nếu có) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=)).  
  - `cod` (int): Số tiền thu hộ (COD, nếu có) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=)).  
  - `return_cost` (int): Phí hoàn (nếu có) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=)).  
- `sender` (Object): **Đối tượng thông tin người gửi**, gồm:  
  - `name` (String): Tên người gửi ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=sender%20details)).  
  - `addr` (String): Địa chỉ người gửi ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=sender%20details)).  
  - `city` (String): Thành phố của người gửi ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=%C2%B7%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0city)).  
- `receiver` (Object): **Đối tượng thông tin người nhận**, gồm:  
  - `name` (String): Tên người nhận ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=Object)).  
  - `addr` (String): Địa chỉ người nhận ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=%C2%B7%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0addr)).  
  - `zipcode` (String): Mã bưu điện người nhận ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=%C2%B7%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0zipcode)).  
  - `city` (String): Thành phố người nhận ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=%C2%B7%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0%C2%A0city)).  
- `driver` (Object): **Thông tin tài xế lấy hàng của J&T**, gồm:  
  - `name` (String): Tên tài xế đến lấy hàng ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=J%26T%20Pickup%20driver%20details)).  
- `delivDriver` (Object): **Thông tin tài xế giao hàng của J&T**, gồm:  
  - `name` (String): Tên tài xế giao hàng ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=)).  
  - `phone` (String): Số điện thoại tài xế giao hàng ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=J%26T%20Delivery%20person%20name)).  
- `history` (mảng Object): **Lịch sử trạng thái của lô hàng**. Mỗi phần tử bao gồm các trường: `date_time` (thời gian trạng thái), `city_name` (thành phố), `status` (trạng thái), `status_code` (mã trạng thái), `storeName` (tên cơ sở J&T), `nextSiteName` (cơ sở kế tiếp), `note` (ghi chú), `receiver` (tên người nhận tại thời điểm đó), `driverName` và `driverPhone` (tên và số điện thoại tài xế giao) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=)) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=)).  

## Kiểm tra cước vận chuyển (Tariff Checking)
### API Kiểm tra cước (Tariff Checking API)
**Mô tả:** API này dùng để tính toán cước vận chuyển từ tỉnh/TP gốc đến quận/huyện đích, dựa trên trọng lượng và loại dịch vụ ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=match%20at%20L1240%20TARIFF%20CHECKING)).  

**Phương thức:** `POST` (Content-Type: `application/x-www-form-urlencoded`) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=API%20Details)).  

**Tham số đầu vào:**  
- **Bên ngoài:**  
  | Tên tham số | Kiểu dữ liệu | Bắt buộc | Mô tả                                   |
  |------------|-------------|---------|-----------------------------------------|
  | `data`     | String      | Y       | Dữ liệu yêu cầu chính (JSON) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=data)). |
  | `sign`     | String      | Y       | Chữ ký bảo mật: MD5+Base64 của `data` + *key* ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=sign)). |

- **Bên trong `data`:**  
  | Tên tham số     | Kiểu dữ liệu | Bắt buộc | Mô tả                                                         |
  |-----------------|-------------|---------|---------------------------------------------------------------|
  | `weight`        | double      | Y       | Trọng lượng gói hàng (kg) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=weight)).                 |
  | `sendSiteCode`  | String      | Y       | Mã tỉnh/thành gốc (viết hoa, ví dụ `JAKARTA`) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=sendSiteCode)). |
  | `destAreaCode`  | String      | Y       | Mã quận/huyện đích (viết hoa, ví dụ `KALIDERES`) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=destAreaCode)).|
  | `cusName`       | String      | Y       | Mã khách hàng đối tác (cấp trên Dashboard) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=cusName)).     |
  | `productType`   | String      | Y       | Loại sản phẩm: luôn điền `"EZ"` (như tài liệu hướng dẫn) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=productType)). |

**Đối tượng sử dụng (phản hồi):** Phản hồi JSON gồm các trường: `is_success` (String, `"true"` hoặc `"false"`), `message` (Mô tả lỗi nếu có) và `content`. Trường `content` là một chuỗi JSON biểu diễn mảng các đối tượng dịch vụ, mỗi đối tượng gồm:  
- `name` (String): Tên loại dịch vụ (ví dụ `"EZ"`).  
- `cost` (String): Cước phí tương ứng (đơn vị tiền địa phương) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=)).  

Ví dụ phản hồi thành công: 
```json
{
  "is_success": "true",
  "message": "",
  "content": "[{\"name\":\"EZ\",\"cost\":\"9000\"}]"
}
```

## Hủy đơn hàng (Cancel Order)
### API Hủy đơn hàng (Cancel Order API)
**Mô tả:** API này dùng để hủy một đơn hàng trên hệ thống J&T nếu đơn chưa được xử lý (chưa giao cho tài xế) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=API%20Cancel%20Order)).  

**Phương thức:** `POST` (Content-Type: `application/x-www-form-urlencoded`) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=API%20Details)).  

**Tham số đầu vào:**  
- **Bên ngoài:**  
  | Tên tham số  | Kiểu dữ liệu | Bắt buộc | Mô tả                                                     |
  |-------------|-------------|---------|-----------------------------------------------------------|
  | `data_param`| String      | Y       | Dữ liệu yêu cầu chính: chuỗi JSON chứa mảng chi tiết hủy đơn ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=data_param)). |
  | `data_sign` | String      | Y       | Chữ ký bảo mật: MD5+Base64 của `data_param` + *key* ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=data_sign)).     |

- **Bên trong `data_param`:**  
  | Tên tham số  | Kiểu dữ liệu | Bắt buộc | Mô tả                                               |
  |--------------|--------------|---------|-----------------------------------------------------|
  | `username`   | String       | Y       | Tên đăng nhập (cấp từ Dashboard) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=username)).  |
  | `api_key`    | String       | Y       | Khoá API (cấp từ Dashboard) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=api_key)).       |
  | `orderid`    | String (20)  | Y       | Mã đơn hàng cần hủy (như hệ thống đối tác đã gửi) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=orderid)). |
  | `remark`     | String (30)  | Y       | Lý do hủy đơn (ví dụ `"Canceled by E-Commerce"`) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=remark)). |

**Đối tượng sử dụng (phản hồi):** Phản hồi JSON gồm các trường `success`, `desc` và `detail`. Trường `detail` là mảng các đối tượng kết quả, mỗi đối tượng gồm:  
- `orderid` (String): Mã đơn hàng đã yêu cầu hủy.  
- `status` (String): `"Sukses"` nếu hủy thành công, `"Error"` nếu không thành công.  
- `reason` (String): Mô tả lý do hủy thất bại (nếu có) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=)) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=)).  

Ví dụ phản hồi thành công:
```json
{
  "success": true,
  "desc": "Request berhasil",
  "detail": [
    {
      "orderid": "ORDERID-12345678",
      "status": "Sukses",
      "reason": ""
    }
  ]
}
```  

**Nguồn:** Thông tin được tổng hợp từ tài liệu chính thức của J&T API ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=API%20BASIC%20ORDER)) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=TRACKING%20API)) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=TARIFF%20CHECKING)) ([J&T API](https://developer.jet.co.id/documentation/index#Order#:~:text=API%20Cancel%20Order)).