"""
HTTP Client cho GHTK API

Module này cung cấp các hàm để thực hiện HTTP requests tới GHTK API
với xử lý lỗi và retry logic phù hợp.
"""

import asyncio
import json
from typing import Dict, Any, Optional, Union
from urllib.parse import urljoin, urlencode

# Import với fallback
try:
    from .config import GHTK_BASE_URL, GHTK_DEFAULT_HEADERS
except ImportError:
    try:
        from config import GHTK_BASE_URL, GHTK_DEFAULT_HEADERS
    except ImportError:
        # Fallback cuối cùng - import trực tiếp
        import os
        import sys
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        from config import GHTK_BASE_URL, GHTK_DEFAULT_HEADERS

# Sử dụng MCP's HTTP utilities
try:
    from mcp.shared._httpx_utils import httpx_client
except ImportError:
    # Fallback nếu không có MCP utilities
    import httpx
    httpx_client = httpx.AsyncClient()

async def make_ghtk_request(
    endpoint: str,
    method: str = "GET",
    data: Optional[Dict[str, Any]] = None,
    params: Optional[Dict[str, Any]] = None,
    headers: Optional[Dict[str, str]] = None,
    timeout: int = 30
) -> Dict[str, Any]:
    """
    Thực hiện HTTP request tới GHTK API

    Args:
        endpoint: API endpoint (ví dụ: "services/shipment/order")
        method: HTTP method (GET, POST, PUT, DELETE)
        data: Dữ liệu gửi trong body (cho POST/PUT)
        params: Query parameters
        headers: Headers bổ sung
        timeout: Timeout trong giây

    Returns:
        Dict chứa response data

    Raises:
        Exception: Khi có lỗi API hoặc network
    """

    # Chuẩn bị headers
    request_headers = GHTK_DEFAULT_HEADERS.copy()
    if headers:
        request_headers.update(headers)

    # Tạo URL đầy đủ
    url = urljoin(GHTK_BASE_URL, endpoint)

    # Thêm query parameters nếu có
    if params:
        url += "?" + urlencode(params)

    try:
        # Thực hiện request
        if method.upper() == "GET":
            response = await httpx_client.get(
                url,
                headers=request_headers,
                timeout=timeout
            )
        elif method.upper() == "POST":
            response = await httpx_client.post(
                url,
                headers=request_headers,
                json=data if data else {},
                timeout=timeout
            )
        elif method.upper() == "PUT":
            response = await httpx_client.put(
                url,
                headers=request_headers,
                json=data if data else {},
                timeout=timeout
            )
        elif method.upper() == "DELETE":
            response = await httpx_client.delete(
                url,
                headers=request_headers,
                timeout=timeout
            )
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")

        # Kiểm tra status code
        response.raise_for_status()

        # Xử lý response
        content_type = response.headers.get("content-type", "")

        if "application/json" in content_type:
            # Response JSON
            result = response.json()

            # Kiểm tra success field trong GHTK response
            if isinstance(result, dict) and "success" in result:
                if not result.get("success", False):
                    error_msg = result.get("message", "Unknown GHTK API error")
                    raise Exception(f"GHTK API Error: {error_msg}")

            return result

        elif "application/pdf" in content_type:
            # Response PDF (cho in nhãn)
            return {
                "success": True,
                "content_type": "application/pdf",
                "content": response.content,
                "filename": _extract_filename_from_headers(response.headers)
            }

        else:
            # Response text/html hoặc khác
            return {
                "success": True,
                "content_type": content_type,
                "content": response.text
            }

    except Exception as e:
        # Log lỗi và re-raise
        error_msg = f"GHTK API request failed: {str(e)}"
        print(f"❌ {error_msg}")
        print(f"   URL: {url}")
        print(f"   Method: {method}")
        print(f"   Headers: {request_headers}")
        if data:
            print(f"   Data: {json.dumps(data, ensure_ascii=False, indent=2)}")

        raise Exception(error_msg)

def _extract_filename_from_headers(headers: Dict[str, str]) -> Optional[str]:
    """
    Trích xuất filename từ Content-Disposition header

    Args:
        headers: Response headers

    Returns:
        Filename nếu có, None nếu không
    """
    content_disposition = headers.get("content-disposition", "")
    if "filename=" in content_disposition:
        # Tìm filename trong header
        parts = content_disposition.split("filename=")
        if len(parts) > 1:
            filename = parts[1].strip().strip('"')
            return filename
    return None

# Các hàm helper cho các API endpoint cụ thể

async def ghtk_get_solutions() -> Dict[str, Any]:
    """Lấy danh sách giải pháp GHTK"""
    return await make_ghtk_request("open/api/v1/shop/solutions")

async def ghtk_create_order(order_data: Dict[str, Any]) -> Dict[str, Any]:
    """Tạo đơn hàng GHTK"""
    return await make_ghtk_request(
        "services/shipment/order",
        method="POST",
        data=order_data
    )

async def ghtk_calculate_fee(fee_params: Dict[str, Any]) -> Dict[str, Any]:
    """Tính phí vận chuyển GHTK"""
    return await make_ghtk_request(
        "services/shipment/fee",
        method="GET",
        params=fee_params
    )

async def ghtk_get_order_status(tracking_order: str) -> Dict[str, Any]:
    """Lấy trạng thái đơn hàng GHTK"""
    return await make_ghtk_request(f"services/shipment/v2/{tracking_order}")

async def ghtk_print_label(tracking_order: str, original: str = "portrait", paper_size: str = "A6") -> Dict[str, Any]:
    """In nhãn đơn hàng GHTK"""
    params = {
        "original": original,
        "paper_size": paper_size
    }
    return await make_ghtk_request(
        f"services/label/{tracking_order}",
        params=params
    )

async def ghtk_cancel_order(tracking_order: str) -> Dict[str, Any]:
    """Hủy đơn hàng GHTK"""
    return await make_ghtk_request(
        f"services/shipment/cancel/{tracking_order}",
        method="POST"
    )

async def ghtk_get_pick_addresses() -> Dict[str, Any]:
    """Lấy danh sách địa chỉ lấy hàng"""
    return await make_ghtk_request("services/shipment/list_pick_add")

async def ghtk_get_level4_addresses(province: str, district: str, ward_street: str, address: str = "") -> Dict[str, Any]:
    """Lấy danh sách địa chỉ cấp 4"""
    params = {
        "province": province,
        "district": district,
        "ward_street": ward_street
    }
    if address:
        params["address"] = address

    return await make_ghtk_request(
        "services/address/getAddressLevel4",
        params=params
    )

async def ghtk_search_products(term: str) -> Dict[str, Any]:
    """Tìm kiếm sản phẩm GHTK"""
    params = {"term": term}
    return await make_ghtk_request(
        "services/kho-hang/thong-tin-san-pham",
        params=params
    )
