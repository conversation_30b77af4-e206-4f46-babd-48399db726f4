"""
Utils package cho Shipment MCP Server (GHN + GHTK)

Package này chứa các tiện ích chung như cấu hình và HTTP client
"""

from .config import (
    ShipmentEnvironment,
    # GHN Configuration
    GHN_TOKEN,
    GHN_SHOP_ID,
    GHN_ENVIRONMENT,
    GHN_BASE_URL,
    GHN_DEFAULT_HEADERS,
    # GHTK Configuration
    GHTK_TOKEN,
    GHTK_PARTNER_CODE,
    GHTK_ENVIRONMENT,
    GHTK_BASE_URL,
    GHTK_DEFAULT_HEADERS,
    # J&T Configuration
    JT_USERNAME,
    JT_API_KEY,
    JT_CUSTOMER_CODE,
    JT_ENVIRONMENT,
    JT_BASE_URL,
    JT_DEFAULT_HEADERS,
    # Ahamove Configuration
    AHAMOVE_API_KEY,
    AHAMOVE_TOKEN,
    AHAMOVE_MOBILE,
    AHAMOVE_ENVIRONMENT,
    A<PERSON><PERSON><PERSON>_BASE_URL,
    AHAMOVE_DEFAULT_HEADERS,
    # Backward compatibility
    BASE_URL,
    DEFAULT_HEADERS
)

# Import HTTP clients
try:
    from .api_client import make_ghn_request
except ImportError:
    from .http_client import make_ghn_request

# Import GHTK client
try:
    from .ghtk_client import make_ghtk_request
except ImportError:
    # Tạm thời sử dụng placeholder
    def make_ghtk_request(*args, **kwargs):
        raise NotImplementedError("GHTK client chưa được triển khai")

# Import J&T client
try:
    from .jt_client import make_jt_request
except ImportError:
    # Tạm thời sử dụng placeholder
    def make_jt_request(*args, **kwargs):
        raise NotImplementedError("J&T client chưa được triển khai")

# Import Ahamove client (sẽ tạo sau)
try:
    from .ahamove_client import make_ahamove_request
except ImportError:
    # Tạm thời sử dụng placeholder
    def make_ahamove_request(*args, **kwargs):
        raise NotImplementedError("Ahamove client chưa được triển khai")

__all__ = [
    "ShipmentEnvironment",
    # GHN
    "GHN_TOKEN",
    "GHN_SHOP_ID",
    "GHN_ENVIRONMENT",
    "GHN_BASE_URL",
    "GHN_DEFAULT_HEADERS",
    # GHTK
    "GHTK_TOKEN",
    "GHTK_PARTNER_CODE",
    "GHTK_ENVIRONMENT",
    "GHTK_BASE_URL",
    "GHTK_DEFAULT_HEADERS",
    # J&T
    "JT_USERNAME",
    "JT_API_KEY",
    "JT_CUSTOMER_CODE",
    "JT_ENVIRONMENT",
    "JT_BASE_URL",
    "JT_DEFAULT_HEADERS",
    # Ahamove
    "AHAMOVE_API_KEY",
    "AHAMOVE_TOKEN",
    "AHAMOVE_MOBILE",
    "AHAMOVE_ENVIRONMENT",
    "AHAMOVE_BASE_URL",
    "AHAMOVE_DEFAULT_HEADERS",
    # Backward compatibility
    "BASE_URL",
    "DEFAULT_HEADERS",
    # HTTP clients
    "make_ghn_request",
    "make_ghtk_request",
    "make_jt_request",
    "make_ahamove_request"
]
