"""
J&T Express Resources cho Shipment MCP Server

Module này chứa các resources để lấy dữ liệu từ J&T Express API
"""

import json
from mcp.server.fastmcp import FastMCP

# Import với fallback
try:
    from src.server.redai_system.shipment.utils.jt_client import jt_track_order
except ImportError:
    from utils.jt_client import jt_track_order

def register_jt_resources(mcp: FastMCP):
    """Đăng ký tất cả J&T Express resources với MCP server"""

    @mcp.resource("jt://order/{awb}")
    async def get_jt_order_status(awb: str) -> str:
        """
        Lấy thông tin trạng thái đơn hàng J&T Express
        
        Args:
            awb: Mã vận đơn (AWB) J&T Express
        
        Returns:
            JSON string chứa thông tin chi tiết đơn hàng và lịch sử vận chuyển
        """
        try:
            result = await jt_track_order(awb)
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy thông tin đơn hàng J&T {awb}: {str(e)}"

    @mcp.resource("jt://service-info")
    async def get_jt_service_info() -> str:
        """
        Thông tin về các dịch vụ và hướng dẫn sử dụng J&T Express API
        
        Returns:
            JSON string chứa thông tin chi tiết về dịch vụ J&T
        """
        service_info = {
            "company_info": {
                "name": "J&T Express",
                "country": "Indonesia",
                "description": "Dịch vụ chuyển phát nhanh hàng đầu Đông Nam Á",
                "website": "https://www.jet.co.id",
                "api_docs": "https://developer.jet.co.id/documentation"
            },
            "service_types": {
                "pickup": {
                    "code": 1,
                    "name": "Pickup Service",
                    "description": "J&T sẽ đến lấy hàng tại địa chỉ người gửi",
                    "suitable_for": "Người gửi không thể đến điểm J&T"
                },
                "dropoff": {
                    "code": 6,
                    "name": "Drop-off Service", 
                    "description": "Người gửi mang hàng đến điểm J&T",
                    "suitable_for": "Tiết kiệm chi phí, có điểm J&T gần"
                }
            },
            "express_types": {
                "EZ": {
                    "code": "1",
                    "name": "EZ Service",
                    "description": "Dịch vụ giao hàng thường",
                    "delivery_time": "2-4 ngày (tùy khoảng cách)"
                }
            },
            "coverage_area": {
                "primary": "Indonesia",
                "major_cities": [
                    "Jakarta (JKT)",
                    "Bandung (BDG)",
                    "Surabaya (SBY)",
                    "Medan (MDN)",
                    "Denpasar (DPS)",
                    "Yogyakarta (YGY)",
                    "Semarang (SMG)"
                ]
            },
            "api_features": [
                "Tạo đơn hàng (Basic Order)",
                "Tra cứu vận đơn (Tracking)",
                "Tính cước vận chuyển (Tariff Checking)",
                "Hủy đơn hàng (Cancel Order)"
            ]
        }
        
        return json.dumps(service_info, ensure_ascii=False, indent=2)

    @mcp.resource("jt://city-codes")
    async def get_jt_city_codes() -> str:
        """
        Danh sách mã thành phố J&T Express
        
        Returns:
            JSON string chứa danh sách mã thành phố và khu vực
        """
        city_codes = {
            "format": "3 ký tự viết hoa",
            "major_cities": {
                "JKT": {
                    "name": "Jakarta",
                    "full_name": "DKI Jakarta",
                    "type": "Capital City",
                    "sample_areas": ["JKT001", "JKT002", "JKT003"]
                },
                "BDG": {
                    "name": "Bandung",
                    "full_name": "Kota Bandung",
                    "type": "City",
                    "sample_areas": ["BDG001", "BDG002"]
                },
                "SBY": {
                    "name": "Surabaya",
                    "full_name": "Kota Surabaya",
                    "type": "City",
                    "sample_areas": ["SBY001", "SBY002"]
                },
                "MDN": {
                    "name": "Medan",
                    "full_name": "Kota Medan",
                    "type": "City",
                    "sample_areas": ["MDN001", "MDN002"]
                },
                "DPS": {
                    "name": "Denpasar",
                    "full_name": "Kota Denpasar",
                    "type": "City",
                    "sample_areas": ["DPS001", "DPS002"]
                },
                "YGY": {
                    "name": "Yogyakarta",
                    "full_name": "Daerah Istimewa Yogyakarta",
                    "type": "Special Region",
                    "sample_areas": ["YGY001", "YGY002"]
                },
                "SMG": {
                    "name": "Semarang",
                    "full_name": "Kota Semarang",
                    "type": "City",
                    "sample_areas": ["SMG001", "SMG002"]
                }
            },
            "area_code_format": {
                "pattern": "CITY + 3 digits",
                "example": "JKT001",
                "description": "Mã khu vực gồm mã thành phố + 3 chữ số"
            },
            "note": "Đây chỉ là danh sách mẫu. Để có danh sách đầy đủ và chính xác, vui lòng liên hệ J&T Express hoặc kiểm tra tài liệu API chính thức."
        }
        
        return json.dumps(city_codes, ensure_ascii=False, indent=2)

    @mcp.resource("jt://api-guide")
    async def get_jt_api_guide() -> str:
        """
        Hướng dẫn sử dụng J&T Express API
        
        Returns:
            JSON string chứa hướng dẫn chi tiết về API
        """
        api_guide = {
            "authentication": {
                "method": "Dual Authentication",
                "basic_order_api": {
                    "type": "Data Signing",
                    "process": [
                        "1. Tạo JSON data_param",
                        "2. Kết hợp data_param + api_key",
                        "3. Tạo MD5 hash",
                        "4. Encode Base64 để có data_sign",
                        "5. Gửi form với data_param và data_sign"
                    ]
                },
                "tracking_api": {
                    "type": "Basic Authentication",
                    "process": [
                        "1. Encode username:api_key với Base64",
                        "2. Thêm header Authorization: Basic {encoded}",
                        "3. Gửi form data với awb và eccompanyid"
                    ]
                }
            },
            "request_format": {
                "content_type": "application/x-www-form-urlencoded",
                "method": "POST only",
                "encoding": "UTF-8"
            },
            "data_validation": {
                "phone_numbers": {
                    "format": "+62xxxxxxxxxx",
                    "example": "+6281234567890",
                    "note": "Phải bắt đầu bằng +62 (Indonesia country code)"
                },
                "city_codes": {
                    "format": "3 uppercase letters",
                    "example": "JKT",
                    "validation": "Must be valid J&T city code"
                },
                "area_codes": {
                    "format": "CITY + 3 digits",
                    "example": "JKT001",
                    "validation": "Must match destination city"
                },
                "order_id": {
                    "max_length": 20,
                    "recommendation": "Use unique prefix to avoid conflicts",
                    "example": "SHOP001-ORDER123"
                }
            },
            "response_handling": {
                "success_indicators": {
                    "basic_order": "success: true, status: 'Sukses'",
                    "tracking": "error_id: 0 or null",
                    "tariff": "is_success: 'true'",
                    "cancel": "success: true, status: 'Sukses'"
                },
                "common_errors": [
                    "Invalid signature - Kiểm tra data_sign",
                    "Invalid city code - Sử dụng mã thành phố đúng",
                    "Invalid phone format - Phải có +62",
                    "Order not found - AWB không tồn tại",
                    "Cannot cancel - Đơn đã được xử lý"
                ]
            },
            "best_practices": [
                "Luôn validate dữ liệu trước khi gửi API",
                "Sử dụng prefix riêng cho order_id",
                "Kiểm tra response success indicator",
                "Lưu AWB number để tracking sau này",
                "Test với sandbox trước khi production",
                "Handle timeout và retry logic",
                "Log API calls để debug"
            ],
            "limitations": [
                "Chỉ hỗ trợ Indonesia",
                "Chỉ có POST method",
                "Yêu cầu chữ ký bảo mật",
                "Giới hạn độ dài các trường",
                "Không có bulk operations",
                "Tracking cần AWB number"
            ]
        }
        
        return json.dumps(api_guide, ensure_ascii=False, indent=2)

    @mcp.resource("jt://status-codes")
    async def get_jt_status_codes() -> str:
        """
        Danh sách mã trạng thái đơn hàng J&T Express
        
        Returns:
            JSON string chứa danh sách mã trạng thái và ý nghĩa
        """
        status_codes = {
            "description": "Mã trạng thái trong lịch sử vận chuyển J&T Express",
            "common_statuses": {
                "order_created": "Đơn hàng đã được tạo",
                "picked_up": "Đã lấy hàng từ người gửi",
                "in_transit": "Đang vận chuyển",
                "out_for_delivery": "Đang giao hàng",
                "delivered": "Đã giao thành công",
                "failed_delivery": "Giao hàng thất bại",
                "returned": "Đã hoàn trả",
                "cancelled": "Đã hủy"
            },
            "tracking_fields": {
                "date_time": "Thời gian cập nhật trạng thái",
                "city_name": "Thành phố xử lý",
                "status": "Mô tả trạng thái",
                "status_code": "Mã trạng thái số",
                "storeName": "Tên cơ sở J&T",
                "nextSiteName": "Cơ sở tiếp theo",
                "note": "Ghi chú thêm",
                "receiver": "Người nhận (nếu đã giao)",
                "driverName": "Tên tài xế",
                "driverPhone": "Số điện thoại tài xế"
            },
            "delivery_info": {
                "driver": "Thông tin tài xế lấy hàng",
                "delivDriver": "Thông tin tài xế giao hàng",
                "actual_amount": "Tổng cước phí thực tế",
                "detail_cost": "Chi tiết các loại phí"
            },
            "cost_breakdown": {
                "shipping_cost": "Phí giao hàng chính",
                "add_cost": "Phí phụ thu (đóng gỗ, v.v.)",
                "insurance_cost": "Phí bảo hiểm",
                "cod": "Số tiền thu hộ",
                "return_cost": "Phí hoàn trả (nếu có)"
            },
            "note": "Mã trạng thái cụ thể có thể khác nhau tùy theo phiên bản API và khu vực. Vui lòng tham khảo tài liệu chính thức của J&T Express."
        }
        
        return json.dumps(status_codes, ensure_ascii=False, indent=2)
