#!/usr/bin/env python3
"""
Script test cho RedAI User API MCP Server

Script này kiểm tra:
1. Server có khởi động được không
2. Schema có được load đúng không
3. MCP components có được tạo đúng không
4. HTTP client có được cấu hình đúng không
"""

import asyncio
import sys
import os
from pathlib import Path

# Thêm src vào Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

try:
    from fastmcp import Client
    from server.redai_system.user_api_server import create_user_api_server, load_openapi_schema
except ImportError as e:
    print(f"❌ Lỗi import: {e}")
    print("💡 Hãy đảm bảo đã cài đặt dependencies:")
    print("   pip install fastmcp>=2.3.0 httpx")
    print("   hoặc: pip install -e .")
    sys.exit(1)

async def test_schema_loading():
    """Test việc load OpenAPI schema"""
    print("🧪 Test 1: Load OpenAPI Schema")
    try:
        schema = load_openapi_schema()
        print(f"   ✅ Schema loaded successfully")
        print(f"   📖 Title: {schema.get('info', {}).get('title', 'N/A')}")
        print(f"   🔢 Version: {schema.get('info', {}).get('version', 'N/A')}")
        print(f"   📍 Endpoints: {len(schema.get('paths', {}))} endpoints")
        return True
    except Exception as e:
        print(f"   ❌ Lỗi load schema: {e}")
        return False

async def test_server_creation():
    """Test việc tạo MCP server"""
    print("\n🧪 Test 2: Create MCP Server")
    try:
        mcp = create_user_api_server()
        print(f"   ✅ Server created successfully")
        print(f"   📛 Server name: {mcp.name}")
        return mcp
    except Exception as e:
        print(f"   ❌ Lỗi tạo server: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_mcp_components(mcp):
    """Test các MCP components được tạo"""
    print("\n🧪 Test 3: MCP Components")
    try:
        # Test với in-memory client
        async with Client(mcp) as client:
            # List tools
            tools = await client.list_tools()
            print(f"   🔧 Tools: {len(tools.tools)} tools")
            for tool in tools.tools[:5]:  # Hiển thị 5 tools đầu
                print(f"      • {tool.name}: {tool.description[:50]}...")
            
            # List resources
            resources = await client.list_resources()
            print(f"   📚 Resources: {len(resources.resources)} resources")
            for resource in resources.resources[:5]:  # Hiển thị 5 resources đầu
                print(f"      • {resource.uri}: {resource.description[:50]}...")
            
            # List resource templates
            templates = await client.list_resource_templates()
            print(f"   📋 Resource Templates: {len(templates.resourceTemplates)} templates")
            for template in templates.resourceTemplates[:5]:  # Hiển thị 5 templates đầu
                print(f"      • {template.uriTemplate}: {template.description[:50]}...")
            
        return True
    except Exception as e:
        print(f"   ❌ Lỗi test components: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tool_execution(mcp):
    """Test thực thi một tool (nếu có JWT token)"""
    print("\n🧪 Test 4: Tool Execution")
    
    jwt_token = os.getenv("REDAI_JWT_TOKEN", "")
    if not jwt_token:
        print("   ⚠️  Bỏ qua test này vì chưa có JWT token")
        print("   💡 Thiết lập REDAI_JWT_TOKEN để test tool execution")
        return True
    
    try:
        async with Client(mcp) as client:
            tools = await client.list_tools()
            if not tools.tools:
                print("   ⚠️  Không có tools để test")
                return True
            
            # Tìm tool GET đơn giản để test
            get_tools = [t for t in tools.tools if "get_" in t.name.lower()]
            if not get_tools:
                print("   ⚠️  Không tìm thấy GET tools để test")
                return True
            
            test_tool = get_tools[0]
            print(f"   🧪 Testing tool: {test_tool.name}")
            
            # Thử gọi tool với parameters rỗng
            try:
                result = await client.call_tool(test_tool.name, {})
                print(f"   ✅ Tool executed successfully")
                print(f"   📊 Result type: {type(result.content[0])}")
                return True
            except Exception as tool_error:
                print(f"   ⚠️  Tool execution failed (có thể do thiếu parameters): {tool_error}")
                return True  # Không coi đây là lỗi nghiêm trọng
                
    except Exception as e:
        print(f"   ❌ Lỗi test tool execution: {e}")
        return False

async def main():
    """Chạy tất cả tests"""
    print("🚀 Bắt đầu test RedAI User API MCP Server")
    print("="*60)
    
    # Test 1: Load schema
    if not await test_schema_loading():
        print("\n❌ Test schema loading thất bại!")
        return False
    
    # Test 2: Create server
    mcp = await test_server_creation()
    if not mcp:
        print("\n❌ Test server creation thất bại!")
        return False
    
    # Test 3: MCP components
    if not await test_mcp_components(mcp):
        print("\n❌ Test MCP components thất bại!")
        return False
    
    # Test 4: Tool execution
    if not await test_tool_execution(mcp):
        print("\n❌ Test tool execution thất bại!")
        return False
    
    print("\n" + "="*60)
    print("✅ Tất cả tests đã pass!")
    print("\n💡 Để chạy server:")
    print("   python src/server/redai_system/user_api_server.py")
    print("\n💡 Để test với JWT token:")
    print("   1. Sao chép .env.example thành .env")
    print("   2. Điền REDAI_JWT_TOKEN trong .env")
    print("   3. Chạy lại test này")
    
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test bị dừng bởi người dùng")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Lỗi không mong đợi: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
