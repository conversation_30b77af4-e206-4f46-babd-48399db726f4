#!/usr/bin/env python3
"""
Script test cho RedAI Affiliate MCP Server

Script này kiểm tra:
1. Server có khởi động được không
2. Schema có được load đúng không
3. MCP components có được tạo đúng không
4. HTTP client có được cấu hình đúng không
5. Tools có được tạo từ OpenAPI spec không
"""

import asyncio
import sys
import os
from pathlib import Path

# Thêm src vào Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

try:
    from fastmcp import Client
    from server.redai_system.affiliatte.affiliate_server import (
        affiliate_server, 
        load_openapi_schema,
        create_authenticated_client
    )
except ImportError as e:
    print(f"❌ Lỗi import: {e}")
    print("💡 Hãy đảm bảo đã cài đặt dependencies:")
    print("   pip install fastmcp>=2.3.0 httpx")
    print("   hoặc: pip install -e .")
    sys.exit(1)

async def test_schema_loading():
    """Test việc load OpenAPI schema"""
    print("🔍 Testing schema loading...")
    try:
        schema = load_openapi_schema()
        print(f"✅ Schema loaded successfully")
        print(f"   📖 Title: {schema.get('info', {}).get('title', 'N/A')}")
        print(f"   🔢 Version: {schema.get('info', {}).get('version', 'N/A')}")
        print(f"   📍 Endpoints: {len(schema.get('paths', {}))}")
        
        # Kiểm tra một số endpoints quan trọng
        paths = schema.get('paths', {})
        expected_endpoints = [
            '/user/affiliate/statistics',
            '/user/affiliate/account', 
            '/user/affiliate/withdrawals',
            '/user/affiliate/referral-links',
            '/user/affiliate/business'
        ]
        
        for endpoint in expected_endpoints:
            if endpoint in paths:
                print(f"   ✅ Found endpoint: {endpoint}")
            else:
                print(f"   ❌ Missing endpoint: {endpoint}")
        
        return True
    except Exception as e:
        print(f"❌ Schema loading failed: {e}")
        return False

async def test_http_client():
    """Test việc tạo HTTP client"""
    print("\n🔍 Testing HTTP client creation...")
    try:
        # Test client không có auth
        client = create_authenticated_client("https://api.redai.com")
        print("✅ HTTP client created successfully (no auth)")
        print(f"   🌐 Base URL: {client.base_url}")
        print(f"   📋 Headers: {dict(client.headers)}")
        
        # Test client có auth
        client_with_auth = create_authenticated_client("https://api.redai.com", "test_token")
        print("✅ HTTP client created successfully (with auth)")
        print(f"   🔑 Has Authorization header: {'Authorization' in client_with_auth.headers}")
        
        await client.aclose()
        await client_with_auth.aclose()
        return True
    except Exception as e:
        print(f"❌ HTTP client creation failed: {e}")
        return False

async def test_mcp_server_creation():
    """Test việc tạo MCP server"""
    print("\n🔍 Testing MCP server creation...")
    try:
        # Tạo server không có auth
        mcp_server = affiliate_server.create_server_with_auth()
        print("✅ MCP server created successfully (no auth)")
        print(f"   📛 Server name: {mcp_server.name}")
        
        # Tạo server có auth
        mcp_server_with_auth = affiliate_server.create_server_with_auth("test_token")
        print("✅ MCP server created successfully (with auth)")
        print(f"   📛 Server name: {mcp_server_with_auth.name}")
        
        return True
    except Exception as e:
        print(f"❌ MCP server creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_mcp_tools_and_resources():
    """Test việc tạo tools và resources từ OpenAPI"""
    print("\n🔍 Testing MCP tools and resources...")
    try:
        mcp_server = affiliate_server.create_server_with_auth()
        
        # Test với in-memory client
        async with Client(mcp_server) as client:
            # List tools
            tools_response = await client.list_tools()
            tools = tools_response.tools
            print(f"✅ Found {len(tools)} tools")
            
            # In ra một số tools quan trọng
            important_tools = [
                'update_bearer_token',
                'check_auth_status', 
                'get_affiliate_summary',
                'calculate_commission'
            ]
            
            tool_names = [tool.name for tool in tools]
            for tool_name in important_tools:
                if tool_name in tool_names:
                    print(f"   ✅ Found tool: {tool_name}")
                else:
                    print(f"   ❌ Missing tool: {tool_name}")
            
            # List resources
            try:
                resources_response = await client.list_resources()
                resources = resources_response.resources
                print(f"✅ Found {len(resources)} resources")
            except Exception as e:
                print(f"⚠️  Resources listing failed (might be normal): {e}")
            
            # Test một tool đơn giản
            try:
                result = await client.call_tool("get_affiliate_summary", {})
                print("✅ Tool call successful")
                print(f"   📄 Result preview: {result.content[0].text[:100]}...")
            except Exception as e:
                print(f"⚠️  Tool call failed (might need auth): {e}")
        
        return True
    except Exception as e:
        print(f"❌ MCP tools/resources test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_commission_calculation():
    """Test tool tính toán hoa hồng"""
    print("\n🔍 Testing commission calculation tool...")
    try:
        mcp_server = affiliate_server.create_server_with_auth()
        
        async with Client(mcp_server) as client:
            # Test tính toán hoa hồng
            result = await client.call_tool("calculate_commission", {
                "amount": 1000000,
                "commission_rate": 5.5
            })
            print("✅ Commission calculation successful")
            print(f"   📄 Result: {result.content[0].text}")
        
        return True
    except Exception as e:
        print(f"❌ Commission calculation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Chạy tất cả tests"""
    print("🚀 Starting RedAI Affiliate MCP Server Tests")
    print("=" * 60)
    
    tests = [
        ("Schema Loading", test_schema_loading),
        ("HTTP Client", test_http_client),
        ("MCP Server Creation", test_mcp_server_creation),
        ("MCP Tools & Resources", test_mcp_tools_and_resources),
        ("Commission Calculation", test_commission_calculation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Tổng kết
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Affiliate server is ready to use.")
        print("\n💡 Next steps:")
        print("   1. Run the server: python src/server/redai_system/affiliatte/affiliate_server.py")
        print("   2. Connect from client: http://127.0.0.1:8004/mcp")
        print("   3. Use update_bearer_token tool to authenticate")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test runner crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
