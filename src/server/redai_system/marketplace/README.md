# RedAI Marketplace MCP Server

Server MCP cho RedAI Marketplace Module API sử dụng FastMCP framework với Streamable HTTP transport.

## Tổng quan

Server này tự động tạo các MCP tools và resources từ `swagger.json`, cung cấp giao diện MCP cho tất cả các endpoint của Marketplace Module API.

## Tính năng chính

### 🛠️ Tools (Hành động)
- **Quản lý sản phẩm**: <PERSON><PERSON><PERSON>, c<PERSON><PERSON> nh<PERSON>t, x<PERSON><PERSON>, gửi duyệt sản phẩm
- **Quản lý giỏ hàng**: <PERSON><PERSON><PERSON><PERSON>, c<PERSON><PERSON> nh<PERSON>, xóa sản phẩm trong giỏ hàng
- **Thanh toán**: Xử lý thanh toán sản phẩm bằng R-Point
- **Authentication**: Quản lý Bearer token authentication

### 📚 Resources (Dữ liệu)
- **<PERSON><PERSON> sách sản phẩm**: Với filtering và pagination
- **Chi tiết sản phẩm**: Thông tin chi tiết từng sản phẩm
- **Thông tin giỏ hàng**: <PERSON><PERSON> sách sản phẩm trong giỏ hàng
- **Lịch sử mua hàng**: Theo dõi các đơn hàng đã mua

### 🔐 Authentication
- Bearer token authentication từ client
- Tools để cập nhật và kiểm tra trạng thái authentication

### 🚀 Transport
- **Streamable HTTP** (mặc định): `http://127.0.0.1:8003/mcp`
- **SSE**: `http://127.0.0.1:8003/sse`
- **STDIO**: Command line interface

## Cài đặt và chạy

### Yêu cầu
```bash
pip install fastmcp httpx
```

### Chạy server
```bash
# Streamable HTTP (mặc định)
python marketplace_server.py

# Hoặc chỉ định transport cụ thể
python marketplace_server.py streamable-http
python marketplace_server.py sse
python marketplace_server.py stdio
```

### Biến môi trường
```bash
# API Configuration
REDAI_MARKETPLACE_API_BASE_URL=https://api.redai.com

# Server Configuration
MARKETPLACE_HTTP_HOST=127.0.0.1
MARKETPLACE_HTTP_PORT=8003
MARKETPLACE_HTTP_PATH=/mcp
MARKETPLACE_TRANSPORT=streamable-http
```

## Cấu trúc API

### Products (Sản phẩm)
- `GET /user/marketplace/products` - Lấy danh sách sản phẩm
- `POST /user/marketplace/products` - Tạo sản phẩm mới
- `GET /user/marketplace/products/{id}` - Chi tiết sản phẩm
- `PUT /user/marketplace/products/{id}` - Cập nhật sản phẩm
- `DELETE /user/marketplace/products/{id}` - Xóa sản phẩm
- `POST /user/marketplace/products/{id}/pending` - Gửi duyệt sản phẩm
- `DELETE /user/marketplace/products/batch` - Xóa nhiều sản phẩm

### Cart (Giỏ hàng)
- `GET /user/marketplace/cart` - Lấy thông tin giỏ hàng
- `POST /user/marketplace/cart` - Thêm sản phẩm vào giỏ hàng
- `PUT /user/marketplace/cart/{id}` - Cập nhật số lượng sản phẩm
- `DELETE /user/marketplace/cart/batch` - Xóa nhiều sản phẩm khỏi giỏ hàng

### Orders (Đơn hàng)
- `GET /user/marketplace/orders/purchase-history` - Lịch sử mua hàng

### Payment (Thanh toán)
- `POST /user/marketplace/payment` - Thanh toán sản phẩm

## Sử dụng với MCP Client

### Kết nối với FastMCP Client
```python
from fastmcp import Client

async def main():
    # Kết nối qua Streamable HTTP
    async with Client("http://127.0.0.1:8003/mcp") as client:
        # Cập nhật authentication
        await client.call_tool("update_bearer_token", {
            "bearer_token": "your_bearer_token_here"
        })
        
        # Lấy danh sách sản phẩm
        products = await client.call_tool("get_user_marketplace_products", {
            "page": 1,
            "limit": 10,
            "category": "AGENT"
        })
        
        # Thêm sản phẩm vào giỏ hàng
        await client.call_tool("post_user_marketplace_cart", {
            "productId": 123,
            "quantity": 1
        })
```

### Cấu hình MCP Standard
```json
{
  "mcpServers": {
    "marketplace": {
      "url": "http://127.0.0.1:8003/mcp",
      "env": {
        "BEARER_TOKEN": "your_bearer_token_here"
      }
    }
  }
}
```

## Tools có sẵn

### Authentication Tools
- `update_bearer_token(bearer_token: str)` - Cập nhật Bearer token
- `check_auth_status()` - Kiểm tra trạng thái authentication
- `get_marketplace_summary()` - Lấy tổng quan marketplace

### Auto-generated Tools (từ OpenAPI)
Tất cả các endpoint trong swagger.json sẽ được tự động tạo thành tools với tên theo format:
- `{method}_{path_with_underscores}`
- Ví dụ: `get_user_marketplace_products`, `post_user_marketplace_cart`

## Troubleshooting

### Lỗi thường gặp

1. **File swagger.json không tìm thấy**
   ```
   FileNotFoundError: Không tìm thấy file schema tại: ...
   ```
   - Đảm bảo file `swagger.json` có trong cùng thư mục với `marketplace_server.py`

2. **Lỗi authentication**
   ```
   401 Unauthorized
   ```
   - Sử dụng tool `update_bearer_token` để cấu hình authentication
   - Kiểm tra Bearer token có hợp lệ không

3. **Lỗi kết nối**
   ```
   Connection refused
   ```
   - Kiểm tra server có đang chạy không
   - Kiểm tra port và host configuration

### Debug mode
```bash
# Chạy với debug information
python marketplace_server.py streamable-http
```

## Phát triển

### Thêm custom tools
Chỉnh sửa method `_add_marketplace_tools()` trong class `MarketplaceServer` để thêm các tools tùy chỉnh.

### Tùy chỉnh authentication
Chỉnh sửa method `_add_auth_tools()` để thêm các phương thức authentication khác.

## Tài liệu tham khảo

- [FastMCP Documentation](https://gofastmcp.com)
- [Model Context Protocol](https://modelcontextprotocol.io)
- [OpenAPI Specification](https://swagger.io/specification/)
