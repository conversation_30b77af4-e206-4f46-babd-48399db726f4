"""
Repository pattern cho database operations.

Module này cung cấp các repository class để thực hiện các thao tác CRUD với database.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy import select, update, delete, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from .models import User, Agent, Tool, Resource


class BaseRepository:
    """Base repository class với các phương thức chung."""
    
    def __init__(self, session: AsyncSession):
        self.session = session


class UserRepository(BaseRepository):
    """Repository cho User model."""
    
    async def create(self, **kwargs) -> User:
        """Tạo user mới."""
        user = User(**kwargs)
        self.session.add(user)
        await self.session.flush()
        await self.session.refresh(user)
        return user
    
    async def get_by_id(self, user_id: int) -> Optional[User]:
        """Lấy user theo ID."""
        result = await self.session.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_username(self, username: str) -> Optional[User]:
        """Lấy user theo username."""
        result = await self.session.execute(
            select(User).where(User.username == username)
        )
        return result.scalar_one_or_none()
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """Lấy user theo email."""
        result = await self.session.execute(
            select(User).where(User.email == email)
        )
        return result.scalar_one_or_none()
    
    async def get_by_api_key(self, api_key: str) -> Optional[User]:
        """Lấy user theo API key."""
        result = await self.session.execute(
            select(User).where(User.api_key == api_key)
        )
        return result.scalar_one_or_none()
    
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Lấy danh sách users."""
        result = await self.session.execute(
            select(User).offset(skip).limit(limit)
        )
        return result.scalars().all()
    
    async def update(self, user_id: int, **kwargs) -> Optional[User]:
        """Cập nhật user."""
        await self.session.execute(
            update(User).where(User.id == user_id).values(**kwargs)
        )
        return await self.get_by_id(user_id)
    
    async def delete(self, user_id: int) -> bool:
        """Xóa user."""
        result = await self.session.execute(
            delete(User).where(User.id == user_id)
        )
        return result.rowcount > 0


class AgentRepository(BaseRepository):
    """Repository cho Agent model."""
    
    async def create(self, **kwargs) -> Agent:
        """Tạo agent mới."""
        agent = Agent(**kwargs)
        self.session.add(agent)
        await self.session.flush()
        await self.session.refresh(agent)
        return agent
    
    async def get_by_id(self, agent_id: int) -> Optional[Agent]:
        """Lấy agent theo ID với relationships."""
        result = await self.session.execute(
            select(Agent)
            .options(selectinload(Agent.user), selectinload(Agent.tools), selectinload(Agent.resources))
            .where(Agent.id == agent_id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_user_id(self, user_id: int, skip: int = 0, limit: int = 100) -> List[Agent]:
        """Lấy danh sách agents của user."""
        result = await self.session.execute(
            select(Agent)
            .where(Agent.user_id == user_id)
            .offset(skip).limit(limit)
        )
        return result.scalars().all()
    
    async def get_by_name(self, user_id: int, name: str) -> Optional[Agent]:
        """Lấy agent theo tên trong phạm vi user."""
        result = await self.session.execute(
            select(Agent).where(and_(Agent.user_id == user_id, Agent.name == name))
        )
        return result.scalar_one_or_none()
    
    async def update(self, agent_id: int, **kwargs) -> Optional[Agent]:
        """Cập nhật agent."""
        await self.session.execute(
            update(Agent).where(Agent.id == agent_id).values(**kwargs)
        )
        return await self.get_by_id(agent_id)
    
    async def delete(self, agent_id: int) -> bool:
        """Xóa agent."""
        result = await self.session.execute(
            delete(Agent).where(Agent.id == agent_id)
        )
        return result.rowcount > 0


class ToolRepository(BaseRepository):
    """Repository cho Tool model."""
    
    async def create(self, **kwargs) -> Tool:
        """Tạo tool mới."""
        tool = Tool(**kwargs)
        self.session.add(tool)
        await self.session.flush()
        await self.session.refresh(tool)
        return tool
    
    async def get_by_id(self, tool_id: int) -> Optional[Tool]:
        """Lấy tool theo ID."""
        result = await self.session.execute(
            select(Tool).options(selectinload(Tool.agent)).where(Tool.id == tool_id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_agent_id(self, agent_id: int) -> List[Tool]:
        """Lấy danh sách tools của agent."""
        result = await self.session.execute(
            select(Tool).where(Tool.agent_id == agent_id)
        )
        return result.scalars().all()
    
    async def get_by_name(self, agent_id: int, name: str) -> Optional[Tool]:
        """Lấy tool theo tên trong phạm vi agent."""
        result = await self.session.execute(
            select(Tool).where(and_(Tool.agent_id == agent_id, Tool.name == name))
        )
        return result.scalar_one_or_none()
    
    async def get_by_type(self, tool_type: str) -> List[Tool]:
        """Lấy danh sách tools theo loại."""
        result = await self.session.execute(
            select(Tool).where(Tool.tool_type == tool_type)
        )
        return result.scalars().all()
    
    async def update(self, tool_id: int, **kwargs) -> Optional[Tool]:
        """Cập nhật tool."""
        await self.session.execute(
            update(Tool).where(Tool.id == tool_id).values(**kwargs)
        )
        return await self.get_by_id(tool_id)
    
    async def delete(self, tool_id: int) -> bool:
        """Xóa tool."""
        result = await self.session.execute(
            delete(Tool).where(Tool.id == tool_id)
        )
        return result.rowcount > 0


class ResourceRepository(BaseRepository):
    """Repository cho Resource model."""
    
    async def create(self, **kwargs) -> Resource:
        """Tạo resource mới."""
        resource = Resource(**kwargs)
        self.session.add(resource)
        await self.session.flush()
        await self.session.refresh(resource)
        return resource
    
    async def get_by_id(self, resource_id: int) -> Optional[Resource]:
        """Lấy resource theo ID."""
        result = await self.session.execute(
            select(Resource).options(selectinload(Resource.agent)).where(Resource.id == resource_id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_agent_id(self, agent_id: int) -> List[Resource]:
        """Lấy danh sách resources của agent."""
        result = await self.session.execute(
            select(Resource).where(Resource.agent_id == agent_id)
        )
        return result.scalars().all()
    
    async def get_by_uri(self, agent_id: int, uri: str) -> Optional[Resource]:
        """Lấy resource theo URI trong phạm vi agent."""
        result = await self.session.execute(
            select(Resource).where(and_(Resource.agent_id == agent_id, Resource.uri == uri))
        )
        return result.scalar_one_or_none()
    
    async def get_by_mime_type(self, mime_type: str) -> List[Resource]:
        """Lấy danh sách resources theo MIME type."""
        result = await self.session.execute(
            select(Resource).where(Resource.mime_type == mime_type)
        )
        return result.scalars().all()
    
    async def update(self, resource_id: int, **kwargs) -> Optional[Resource]:
        """Cập nhật resource."""
        await self.session.execute(
            update(Resource).where(Resource.id == resource_id).values(**kwargs)
        )
        return await self.get_by_id(resource_id)
    
    async def delete(self, resource_id: int) -> bool:
        """Xóa resource."""
        result = await self.session.execute(
            delete(Resource).where(Resource.id == resource_id)
        )
        return result.rowcount > 0
