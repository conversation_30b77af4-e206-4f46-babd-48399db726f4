{"mcpServers": {"shipment-ghn-ghtk-jt-ahamove": {"command": "python", "args": ["D:/P01_V2/redai-v201-mcp-server/src/server/redai_system/shipment_server.py"], "env": {"GHN_TOKEN": "test_token_12345", "GHN_SHOP_ID": "12345", "GHN_ENVIRONMENT": "test", "GHTK_TOKEN": "test_ghtk_token_12345", "GHTK_PARTNER_CODE": "test_partner_12345", "GHTK_ENVIRONMENT": "test", "JT_USERNAME": "test_jt_username", "JT_API_KEY": "test_jt_api_key_12345", "JT_CUSTOMER_CODE": "test_customer_12345", "JT_ENVIRONMENT": "test", "AHAMOVE_API_KEY": "test_ahamove_api_key_12345", "AHAMOVE_TOKEN": "test_ahamove_token_12345", "AHAMOVE_MOBILE": "84912345678", "AHAMOVE_ENVIRONMENT": "test"}}}}