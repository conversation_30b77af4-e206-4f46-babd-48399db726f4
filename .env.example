# RedAI MCP Server Configuration
# Sao chép file này thành .env và điền các giá trị thực tế

# ============================================================================
# RedAI User API Configuration
# ============================================================================

# Base URL của RedAI User API
REDAI_USER_API_BASE_URL=https://api.redai.com

# JWT Token để xác thực với User API
# Lấy token từ RedAI dashboard hoặc login API
REDAI_JWT_TOKEN=your_jwt_token_here

# HTTP Server Configuration cho User API MCP Server
USER_API_HTTP_HOST=127.0.0.1
USER_API_HTTP_PORT=8001
USER_API_HTTP_PATH=/mcp

# ============================================================================
# RedAI Global API Configuration (cho server khác)
# ============================================================================

# API Key cho RedAI Global API
REDAI_API_KEY=your_api_key_here
REDAI_BASE_URL=https://api.redai.com

# ============================================================================
# Shipment API Configuration (cho shipment server)
# ============================================================================

# GHN (Giao hàng Nhanh) Configuration
GHN_TOKEN=your_ghn_token_here
GHN_SHOP_ID=your_ghn_shop_id_here
GHN_ENVIRONMENT=production
# hoặc sandbox
GHN_HTTP_HOST=127.0.0.1
GHN_HTTP_PORT=8000
GHN_HTTP_PATH=/mcp

# GHTK (Giao hàng Tiết kiệm) Configuration
GHTK_TOKEN=your_ghtk_token_here
GHTK_PARTNER_CODE=your_ghtk_partner_code_here
GHTK_ENVIRONMENT=production
# hoặc sandbox

# J&T Express Configuration
JT_USERNAME=your_jt_username_here
JT_API_KEY=your_jt_api_key_here
JT_CUSTOMER_CODE=your_jt_customer_code_here
JT_ENVIRONMENT=production
# hoặc sandbox

# Ahamove Configuration
AHAMOVE_API_KEY=your_ahamove_api_key_here
AHAMOVE_TOKEN=your_ahamove_token_here
AHAMOVE_MOBILE=your_ahamove_mobile_here
AHAMOVE_ENVIRONMENT=production
# hoặc sandbox

# ============================================================================
# Database Configuration (nếu cần)
# ============================================================================

# Database URL (PostgreSQL, MySQL, SQLite, etc.)
DATABASE_URL=sqlite:///./redai_mcp.db

# ============================================================================
# Logging Configuration
# ============================================================================

# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Log file path (để trống nếu chỉ log ra console)
LOG_FILE=

# ============================================================================
# Development Configuration
# ============================================================================

# Môi trường: development, staging, production
ENVIRONMENT=development

# Debug mode
DEBUG=true
